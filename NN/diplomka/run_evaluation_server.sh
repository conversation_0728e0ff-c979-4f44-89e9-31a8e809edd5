#!/bin/bash

# Evaluačn<PERSON> skript pro spuštění na serveru kajman
# Spustí evaluaci všech finetuned modelů na test datasetu

echo "Spouštím evaluaci modelů na serveru..."
echo "Dataset: /data/prusek/training_small/test"
echo "Modely: /home/<USER>/SpheroSeg/NN/diplomka/scripts/training/outputs"
echo "GPU: $(nvidia-smi --query-gpu=name --format=csv,noheader,nounits)"
echo ""

# Přejdi do správného adresáře
cd /home/<USER>/SpheroSeg/NN/diplomka

# Zkontroluj, zda existují potřebné soubory
if [ ! -f "evaluate_models_server.py" ]; then
    echo "Chyba: evaluate_models_server.py neexistuje!"
    echo "Nejprve synchronizuj soubory ze serveru"
    exit 1
fi

if [ ! -d "/data/prusek/training_small/test" ]; then
    echo "Chyba: Test dataset neexistuje na /data/prusek/training_small/test"
    exit 1
fi

# Aktivuj conda environment (pokud existuje)
if command -v conda &> /dev/null; then
    echo "Aktivuji conda environment..."
    source ~/miniconda3/etc/profile.d/conda.sh
    conda activate pytorch_env 2>/dev/null || conda activate base
fi

# Spusť evaluaci
echo "Spouštím evaluaci..."
python3 evaluate_models_server.py

echo ""
echo "Evaluace dokončena!"
echo "Výsledky jsou v aktuálním adresáři v složce evaluation_results_*"