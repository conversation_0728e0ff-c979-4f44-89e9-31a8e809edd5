"""Test slice notation for skip connections"""

skip_connections = [64, 128, 256, 512, 1024]
print("Original skip connections:", skip_connections)
print("We have 5 skip connections but only 4 decoders")

print("\nOption 1 - Reverse all and take last 4:")
print("skip_connections[::-1][:4] =", skip_connections[::-1][:4])

print("\nOption 2 - Take last 4 and reverse:")  
print("skip_connections[1:][::-1] =", skip_connections[1:][::-1])

print("\nOption 3 - Using slice notation:")
print("skip_connections[-1:0:-1] =", skip_connections[-1:0:-1])

print("\nWhat we need for decoders:")
print("Decoder 0 should get: 1024 (from encoder 4)")
print("Decoder 1 should get: 512 (from encoder 3)")
print("Decoder 2 should get: 256 (from encoder 2)")
print("Decoder 3 should get: 128 (from encoder 1)")
print("(Skip 64 from encoder 0)")