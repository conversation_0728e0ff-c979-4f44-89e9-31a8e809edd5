"""Analyze U-Mamba architecture to understand the correct skip connection flow"""

# Standard U-Net architecture flow:
# 
# Input: 256x256
#   ↓
# Encoder 0: 64 channels, 256x256 → pool → 128x128  [skip: 64@256x256]
#   ↓
# Encoder 1: 128 channels, 128x128 → pool → 64x64   [skip: 128@128x128]
#   ↓
# Encoder 2: 256 channels, 64x64 → pool → 32x32     [skip: 256@64x64]
#   ↓
# Encoder 3: 512 channels, 32x32 → pool → 16x16     [skip: 512@32x32]
#   ↓
# Encoder 4: 1024 channels, 16x16 → pool → 8x8      [skip: 1024@16x16]
#   ↓
# Bottleneck: 1024 channels, 8x8
#   ↓
# Decoder 0: upsample to 16x16 + skip from Encoder 4 (1024@16x16)
#   ↓
# Decoder 1: upsample to 32x32 + skip from Encoder 3 (512@32x32)
#   ↓
# Decoder 2: upsample to 64x64 + skip from Encoder 2 (256@64x64)
#   ↓
# Decoder 3: upsample to 128x128 + skip from Encoder 1 (128@128x128)
#   ↓
# Decoder 4: upsample to 256x256 + skip from Encoder 0 (64@256x256)

print("=== CORRECT U-NET ARCHITECTURE FLOW ===\n")

features = [64, 128, 256, 512, 1024]

# Encoder flow
print("ENCODER:")
spatial_sizes = []
for i, feat in enumerate(features):
    spatial = 256 // (2**i)
    spatial_sizes.append(spatial)
    print(f"  Block {i}: {feat} channels @ {spatial}x{spatial} → pool → {spatial//2}x{spatial//2}")

# Bottleneck
bottleneck_spatial = 256 // (2**len(features))
print(f"\nBOTTLENECK: {features[-1]} channels @ {bottleneck_spatial}x{bottleneck_spatial}")

# Decoder flow
print("\nDECODER:")
for i in range(len(features)):
    decoder_idx = i
    encoder_idx = len(features) - 1 - i
    
    # Current spatial size before upsampling
    current_spatial = bottleneck_spatial * (2**i)
    
    # After upsampling
    upsampled_spatial = current_spatial * 2
    
    # Skip connection
    if encoder_idx >= 0:
        skip_channels = features[encoder_idx]
        skip_spatial = spatial_sizes[encoder_idx]
        
        print(f"  Block {decoder_idx}: upsample to {upsampled_spatial}x{upsampled_spatial} + skip from Encoder {encoder_idx} ({skip_channels}@{skip_spatial}x{skip_spatial})")
        
        if upsampled_spatial != skip_spatial:
            print(f"    ERROR: Spatial mismatch!")
    else:
        print(f"  Block {decoder_idx}: upsample to {upsampled_spatial}x{upsampled_spatial} (no skip)")

print("\n=== ISSUE IDENTIFICATION ===")
print("\nThe U-Mamba model has 5 encoder blocks producing 5 skip connections,")
print("but only 4 decoder blocks. This creates a mismatch.")
print("\nOption 1: Use all 5 skip connections (need 5 decoder blocks)")
print("Option 2: Skip the deepest encoder's skip connection")
print("\nThe model chose Option 2, but implemented it incorrectly.")
print("It should include the skip from the LAST encoder block (1024@16x16),")
print("not exclude it!")