#!/usr/bin/env python3
"""
Analyze memory usage of Advanced ResUNet model
"""

import torch
import torch.nn as nn
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from models.resunet_advanced import AdvancedResUNet
from models.resunet import ResUNet as BasicResUNet

def format_bytes(bytes):
    """Convert bytes to human readable format"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if bytes < 1024.0:
            return f"{bytes:.2f} {unit}"
        bytes /= 1024.0
    return f"{bytes:.2f} TB"

def count_parameters(model):
    """Count trainable and total parameters"""
    trainable = sum(p.numel() for p in model.parameters() if p.requires_grad)
    total = sum(p.numel() for p in model.parameters())
    return trainable, total

def estimate_memory_usage(model, batch_size, img_size, device='cuda'):
    """Estimate memory usage for forward and backward pass"""
    model = model.to(device)
    model.train()
    
    # Create dummy input
    x = torch.randn(batch_size, 3, img_size, img_size).to(device)
    
    # Clear cache
    if device == 'cuda':
        torch.cuda.empty_cache()
        torch.cuda.reset_peak_memory_stats()
        
    # Forward pass
    print(f"\nMemory before forward pass: {format_bytes(torch.cuda.memory_allocated())}")
    
    # Track activations memory
    activations = []
    
    def hook_fn(module, input, output):
        if isinstance(output, torch.Tensor):
            activations.append(output)
    
    # Register hooks on major layers
    hooks = []
    for name, module in model.named_modules():
        if len(list(module.children())) == 0:  # Leaf modules only
            hooks.append(module.register_forward_hook(hook_fn))
    
    # Forward pass
    output = model(x)
    
    print(f"Memory after forward pass: {format_bytes(torch.cuda.memory_allocated())}")
    print(f"Peak memory during forward: {format_bytes(torch.cuda.max_memory_allocated())}")
    
    # Calculate activation memory
    activation_memory = sum(act.numel() * act.element_size() for act in activations)
    print(f"Activation memory: {format_bytes(activation_memory)}")
    print(f"Number of activation tensors: {len(activations)}")
    
    # Clean up hooks
    for hook in hooks:
        hook.remove()
    
    # Backward pass
    loss = output.mean()
    loss.backward()
    
    print(f"Memory after backward pass: {format_bytes(torch.cuda.memory_allocated())}")
    print(f"Peak memory during backward: {format_bytes(torch.cuda.max_memory_allocated())}")
    
    return activations

def analyze_model_layers(model):
    """Analyze memory consumption by layer"""
    print("\n" + "="*80)
    print("Layer-wise Analysis")
    print("="*80)
    
    total_params = 0
    layer_info = []
    
    for name, module in model.named_modules():
        if len(list(module.children())) == 0:  # Leaf modules
            params = sum(p.numel() for p in module.parameters())
            if params > 0:
                param_memory = sum(p.numel() * p.element_size() for p in module.parameters())
                layer_info.append((name, module.__class__.__name__, params, param_memory))
                total_params += params
    
    # Sort by parameter count
    layer_info.sort(key=lambda x: x[2], reverse=True)
    
    print(f"{'Layer Name':<50} {'Type':<20} {'Parameters':<15} {'Memory':<15}")
    print("-"*100)
    
    for name, module_type, params, memory in layer_info[:20]:  # Top 20 layers
        print(f"{name:<50} {module_type:<20} {params:<15,} {format_bytes(memory):<15}")
    
    print(f"\nTotal parameters: {total_params:,}")

def compare_models():
    """Compare basic and advanced ResUNet"""
    print("\n" + "="*80)
    print("Model Comparison")
    print("="*80)
    
    # Basic ResUNet
    basic_model = BasicResUNet(in_channels=3, out_channels=1)
    basic_trainable, basic_total = count_parameters(basic_model)
    
    # Advanced ResUNet
    advanced_model = AdvancedResUNet(in_channels=3, out_channels=1)
    advanced_trainable, advanced_total = count_parameters(advanced_model)
    
    print(f"{'Model':<20} {'Trainable Params':<20} {'Total Params':<20}")
    print("-"*60)
    print(f"{'Basic ResUNet':<20} {basic_trainable:<20,} {basic_total:<20,}")
    print(f"{'Advanced ResUNet':<20} {advanced_trainable:<20,} {advanced_total:<20,}")
    print(f"{'Ratio':<20} {advanced_trainable/basic_trainable:.2f}x")

def check_memory_inefficiencies(model):
    """Check for memory inefficiencies in the model"""
    print("\n" + "="*80)
    print("Memory Efficiency Analysis")
    print("="*80)
    
    issues = []
    
    # Check for large intermediate tensors
    for name, module in model.named_modules():
        # Check for inefficient attention mechanisms
        if 'attention' in name.lower():
            if hasattr(module, 'num_heads'):
                issues.append(f"Attention module '{name}' uses {module.num_heads} heads - consider reducing for memory efficiency")
        
        # Check for unnecessary biases in conv layers followed by norm
        if isinstance(module, nn.Conv2d):
            parent_name = '.'.join(name.split('.')[:-1])
            parent = model
            for part in parent_name.split('.'):
                if part:
                    parent = getattr(parent, part)
            
            # Check if this conv is followed by normalization
            if hasattr(parent, 'norm1') or hasattr(parent, 'norm2'):
                if module.bias is not None:
                    issues.append(f"Conv layer '{name}' has bias but is followed by normalization - this wastes memory")
    
    # Print issues
    if issues:
        print("Found potential memory inefficiencies:")
        for i, issue in enumerate(issues, 1):
            print(f"{i}. {issue}")
    else:
        print("No obvious memory inefficiencies found")

def main():
    """Main analysis function"""
    print("="*80)
    print("Advanced ResUNet Memory Analysis")
    print("="*80)
    
    # Model configuration
    batch_size = 2
    img_size = 1024
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    print(f"\nConfiguration:")
    print(f"- Batch size: {batch_size}")
    print(f"- Image size: {img_size}x{img_size}")
    print(f"- Device: {device}")
    
    # Create model
    model = AdvancedResUNet(in_channels=3, out_channels=1)
    trainable, total = count_parameters(model)
    
    print(f"\nModel Statistics:")
    print(f"- Trainable parameters: {trainable:,}")
    print(f"- Total parameters: {total:,}")
    print(f"- Model size: {format_bytes(sum(p.numel() * p.element_size() for p in model.parameters()))}")
    
    # Theoretical memory calculation
    print("\n" + "="*80)
    print("Theoretical Memory Requirements")
    print("="*80)
    
    # Input tensor
    input_memory = batch_size * 3 * img_size * img_size * 4  # float32
    print(f"Input tensor: {format_bytes(input_memory)}")
    
    # Model parameters
    param_memory = sum(p.numel() * p.element_size() for p in model.parameters())
    print(f"Model parameters: {format_bytes(param_memory)}")
    
    # Gradients (same size as parameters)
    grad_memory = param_memory
    print(f"Gradients: {format_bytes(grad_memory)}")
    
    # Optimizer states (Adam uses 2x parameters for momentum)
    optimizer_memory = param_memory * 2
    print(f"Optimizer states (Adam): {format_bytes(optimizer_memory)}")
    
    # Estimated activation memory (rough estimate: 10-20x input size for UNet-like architectures)
    activation_factor = 15  # Conservative estimate
    activation_memory = input_memory * activation_factor
    print(f"Estimated activations: {format_bytes(activation_memory)} (factor: {activation_factor}x)")
    
    # Total
    total_memory = input_memory + param_memory + grad_memory + optimizer_memory + activation_memory
    print(f"\nTotal estimated memory: {format_bytes(total_memory)}")
    
    # Compare models
    compare_models()
    
    # Analyze model layers
    analyze_model_layers(model)
    
    # Check for inefficiencies
    check_memory_inefficiencies(model)
    
    # Actual memory test (if CUDA available)
    if device == 'cuda' and torch.cuda.is_available():
        print("\n" + "="*80)
        print("Actual Memory Usage Test")
        print("="*80)
        
        try:
            activations = estimate_memory_usage(model, batch_size, img_size, device)
            
            # Analyze large activations
            print("\n" + "="*80)
            print("Largest Activation Tensors")
            print("="*80)
            
            # Sort activations by size
            activation_sizes = [(act.shape, act.numel() * act.element_size()) for act in activations]
            activation_sizes.sort(key=lambda x: x[1], reverse=True)
            
            print(f"{'Shape':<30} {'Memory':<15}")
            print("-"*45)
            for shape, size in activation_sizes[:10]:  # Top 10
                print(f"{str(shape):<30} {format_bytes(size):<15}")
                
        except RuntimeError as e:
            print(f"\nCUDA out of memory during test: {e}")
            print("This confirms the memory issues with the current model configuration.")

if __name__ == "__main__":
    main()