#!/usr/bin/env python3
"""
Profile memory bottlenecks in Advanced ResUNet
"""

import torch
import torch.nn as nn
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from models.resunet_advanced import AdvancedResUNet, MultiStageAttention, TripletAttention, LightweightSelfAttention

def analyze_attention_memory():
    """Analyze memory usage of different attention mechanisms"""
    print("="*80)
    print("Attention Mechanism Memory Analysis")
    print("="*80)
    
    # Test configuration
    batch_size = 2
    channels = 512
    height = width = 64  # Feature map size at bottleneck
    
    # Create input tensor
    x = torch.randn(batch_size, channels, height, width).cuda()
    
    attention_modules = {
        'MultiStageAttention': MultiStageAttention(channels),
        'TripletAttention': TripletAttention(channels),
        'LightweightSelfAttention': LightweightSelfAttention(channels)
    }
    
    print(f"\nInput shape: {x.shape}")
    print(f"Input memory: {x.numel() * 4 / 1024**2:.2f} MB\n")
    
    for name, module in attention_modules.items():
        module = module.cuda()
        torch.cuda.empty_cache()
        torch.cuda.reset_peak_memory_stats()
        
        # Measure memory before
        before = torch.cuda.memory_allocated()
        
        # Forward pass
        with torch.no_grad():
            out = module(x)
        
        # Measure memory after
        after = torch.cuda.memory_allocated()
        peak = torch.cuda.max_memory_allocated()
        
        # Parameter count
        params = sum(p.numel() for p in module.parameters())
        
        print(f"{name}:")
        print(f"  Parameters: {params:,}")
        print(f"  Memory increase: {(after - before) / 1024**2:.2f} MB")
        print(f"  Peak memory: {peak / 1024**2:.2f} MB")
        print(f"  Output shape: {out.shape}")
        print()

def profile_model_stages():
    """Profile memory usage at different stages of the model"""
    print("="*80)
    print("Model Stage Memory Profiling")
    print("="*80)
    
    batch_size = 2
    img_size = 1024
    
    # Create model
    model = AdvancedResUNet(in_channels=3, out_channels=1).cuda()
    model.eval()
    
    # Create hooks to track memory at each stage
    memory_profile = {}
    
    def make_hook(name):
        def hook(module, input, output):
            memory_profile[name] = {
                'allocated': torch.cuda.memory_allocated() / 1024**2,  # MB
                'reserved': torch.cuda.memory_reserved() / 1024**2,    # MB
                'output_shape': output.shape if isinstance(output, torch.Tensor) else str(type(output))
            }
        return hook
    
    # Register hooks on major components
    hooks = []
    hook_points = {
        'init_conv': model.init_conv,
        'encoder1': model.encoder1,
        'encoder2': model.encoder2,
        'encoder3': model.encoder3,
        'encoder4': model.encoder4,
        'bottleneck': model.bottleneck,
        'decoder4': model.decoder4,
        'decoder3': model.decoder3,
        'decoder2': model.decoder2,
        'decoder1': model.decoder1,
        'final_conv': model.final_conv
    }
    
    for name, module in hook_points.items():
        hooks.append(module.register_forward_hook(make_hook(name)))
    
    # Run forward pass
    x = torch.randn(batch_size, 3, img_size, img_size).cuda()
    
    torch.cuda.empty_cache()
    torch.cuda.reset_peak_memory_stats()
    
    print(f"\nInput shape: {x.shape}")
    print(f"Input memory: {x.numel() * 4 / 1024**2:.2f} MB\n")
    
    with torch.no_grad():
        output = model(x)
    
    # Clean up hooks
    for hook in hooks:
        hook.remove()
    
    # Print memory profile
    print(f"{'Stage':<15} {'Allocated (MB)':<15} {'Reserved (MB)':<15} {'Output Shape':<30}")
    print("-"*75)
    
    for stage in ['init_conv', 'encoder1', 'encoder2', 'encoder3', 'encoder4', 
                  'bottleneck', 'decoder4', 'decoder3', 'decoder2', 'decoder1', 'final_conv']:
        if stage in memory_profile:
            info = memory_profile[stage]
            print(f"{stage:<15} {info['allocated']:<15.2f} {info['reserved']:<15.2f} {str(info['output_shape']):<30}")
    
    print(f"\nFinal output shape: {output.shape}")
    print(f"Peak memory usage: {torch.cuda.max_memory_allocated() / 1024**2:.2f} MB")

def identify_memory_spikes():
    """Identify operations that cause memory spikes"""
    print("\n" + "="*80)
    print("Memory Spike Analysis")
    print("="*80)
    
    # Test problematic operations
    batch_size = 2
    channels = 1024  # Bottleneck channels
    size = 64  # Feature map size at bottleneck
    
    operations = {
        'Conv2d (3x3)': lambda: nn.Conv2d(channels, channels, 3, padding=1),
        'ConvTranspose2d': lambda: nn.ConvTranspose2d(channels, channels//2, 2, stride=2),
        'Self-Attention': lambda: LightweightSelfAttention(channels),
        'Triplet Attention': lambda: TripletAttention(channels),
        'Concatenation': None  # Special case
    }
    
    x = torch.randn(batch_size, channels, size, size).cuda()
    
    for op_name, op_factory in operations.items():
        torch.cuda.empty_cache()
        torch.cuda.reset_peak_memory_stats()
        
        before = torch.cuda.memory_allocated()
        
        if op_name == 'Concatenation':
            # Test concatenation (common in U-Net)
            x2 = torch.randn_like(x)
            with torch.no_grad():
                out = torch.cat([x, x2], dim=1)
        else:
            op = op_factory().cuda()
            with torch.no_grad():
                out = op(x)
        
        after = torch.cuda.memory_allocated()
        peak = torch.cuda.max_memory_allocated()
        
        print(f"\n{op_name}:")
        print(f"  Input shape: {x.shape}")
        print(f"  Output shape: {out.shape}")
        print(f"  Memory increase: {(after - before) / 1024**2:.2f} MB")
        print(f"  Peak memory: {peak / 1024**2:.2f} MB")

def suggest_optimizations():
    """Suggest memory optimizations"""
    print("\n" + "="*80)
    print("Memory Optimization Suggestions")
    print("="*80)
    
    print("\n1. BOTTLENECK OPTIMIZATION:")
    print("   - Current: 512 -> 1024 channels")
    print("   - Suggested: 512 -> 768 channels (25% reduction)")
    print("   - Memory savings: ~25% at bottleneck stage")
    
    print("\n2. ATTENTION MECHANISMS:")
    print("   - Replace MultiStageAttention with single TripletAttention in some layers")
    print("   - Use gradient checkpointing for attention modules")
    print("   - Reduce attention heads in LightweightSelfAttention from 8 to 4")
    
    print("\n3. FEATURE CHANNELS:")
    print("   - Current: [64, 128, 256, 512]")
    print("   - Suggested: [48, 96, 192, 384] (25% reduction)")
    print("   - Maintains ~40M parameters but reduces activation memory")
    
    print("\n4. GRADIENT CHECKPOINTING:")
    print("   - Enable for encoder blocks to trade compute for memory")
    print("   - Can reduce memory by 30-50% at cost of 20-30% slower training")
    
    print("\n5. MIXED PRECISION OPTIMIZATIONS:")
    print("   - Use torch.cuda.amp more aggressively")
    print("   - Keep attention computations in fp16")
    
    print("\n6. ARCHITECTURAL CHANGES:")
    print("   - Remove redundant normalization after attention")
    print("   - Use depthwise separable convolutions in decoder")
    print("   - Reduce number of residual blocks in deeper layers")

def calculate_actual_requirements():
    """Calculate actual memory requirements for batch size 2"""
    print("\n" + "="*80)
    print("Actual Memory Requirements (Batch Size 2)")
    print("="*80)
    
    # Model parameters
    model = AdvancedResUNet(in_channels=3, out_channels=1)
    param_count = sum(p.numel() for p in model.parameters())
    param_memory = param_count * 4  # float32
    
    print(f"\nModel Parameters: {param_count:,} ({param_memory / 1024**3:.2f} GB)")
    
    # For batch size 2, 1024x1024 images
    batch_size = 2
    img_size = 1024
    
    # Memory breakdown
    print("\nMemory Breakdown:")
    print(f"1. Input tensor: {batch_size * 3 * img_size * img_size * 4 / 1024**3:.2f} GB")
    print(f"2. Model parameters: {param_memory / 1024**3:.2f} GB")
    print(f"3. Gradients: {param_memory / 1024**3:.2f} GB")
    print(f"4. Optimizer states (AdamW): {param_memory * 2 / 1024**3:.2f} GB")
    
    # Activation memory estimate
    # For Advanced ResUNet with heavy attention mechanisms
    activation_memory_gb = 35  # Empirical estimate based on architecture
    print(f"5. Activations (empirical): ~{activation_memory_gb:.1f} GB")
    
    total = (batch_size * 3 * img_size * img_size * 4 + param_memory * 4) / 1024**3 + activation_memory_gb
    print(f"\nTotal estimated: ~{total:.1f} GB")
    print(f"Available GPU memory: 44.39 GB")
    print(f"Margin: {44.39 - total:.1f} GB")
    
    if total > 44.39:
        print("\n⚠️ MEMORY OVERFLOW DETECTED!")
        print("The model requires more memory than available on a single L40S GPU.")

def main():
    """Run all analyses"""
    # Check CUDA availability
    if not torch.cuda.is_available():
        print("CUDA not available. This script requires a GPU.")
        return
    
    # Run analyses
    analyze_attention_memory()
    profile_model_stages()
    identify_memory_spikes()
    suggest_optimizations()
    calculate_actual_requirements()

if __name__ == "__main__":
    main()