============================================================
COMPLETE DATASET SUMMARY
============================================================
Train samples: 19,491
Val samples:   2,539
Test samples:  653
Total samples: 22,683
============================================================

TRAIN Dataset Summary:
  Total images found: 19491
  Valid image-mask pairs: 19491
  Images without masks: 0
  Final train dataset size: 19491

VAL Dataset Summary:
  Total images found: 2539
  Valid image-mask pairs: 2539
  Images without masks: 0
  Final val dataset size: 2539
Loading pretrained model from ./outputs/hrnet_pretrained/best_model.pth

TRAIN Dataset Summary:
  Total images found: 19491
  Valid image-mask pairs: 19491
  Images without masks: 0
  Final train dataset size: 19491

VAL Dataset Summary:
  Total images found: 2539
  Valid image-mask pairs: 2539
  Images without masks: 0
  Final val dataset size: 2539

TEST Dataset Summary:
  Total images found: 653
  Valid image-mask pairs: 653
  Images without masks: 0
  Final test dataset size: 653

============================================================
COMPLETE DATASET SUMMARY
============================================================
Dataset path: /data/prusek/training_small
Image size: 1024x1024
------------------------------------------------------------
Train samples: 19,491
Val samples:   2,539
Test samples:  653
------------------------------------------------------------
Total samples: 22,683
============================================================

Loading pretrained model from ./outputs/hrnet_pretrained/best_model.pth
[rank0]:[W715 11:44:59.682853198 ProcessGroupNCCL.cpp:1479] Warning: WARNING: destroy_process_group() was not called before program exit, which can leak resources. For more info, please see https://pytorch.org/docs/stable/distributed.html#shutdown (function operator())
W0715 11:45:00.085284 3441927 torch/multiprocessing/spawn.py:169] Terminating process 3442061 via signal SIGTERM
Traceback (most recent call last):
  File "/home/<USER>/SpheroSeg/NN/diplomka/CNN_main_spheroid.py", line 1229, in <module>
    main()
  File "/home/<USER>/SpheroSeg/NN/diplomka/CNN_main_spheroid.py", line 1224, in main
    mp.spawn(train, args=(world_size, args), nprocs=world_size, join=True)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 340, in spawn
    return start_processes(fn, args, nprocs, join, daemon, start_method="spawn")
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 296, in start_processes
    while not context.join():
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 215, in join
    raise ProcessRaisedException(msg, error_index, failed_process.pid)
torch.multiprocessing.spawn.ProcessRaisedException: 

-- Process 0 terminated with the following error:
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 90, in _wrap
    fn(i, *args)
  File "/home/<USER>/SpheroSeg/NN/diplomka/CNN_main_spheroid.py", line 963, in train
    checkpoint = torch.load(args.pretrained_path, map_location=device)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/serialization.py", line 1524, in load
    raise pickle.UnpicklingError(_get_wo_message(str(e))) from None
_pickle.UnpicklingError: Weights only load failed. This file can still be loaded, to do so you have two options, [1mdo those steps only if you trust the source of the checkpoint[0m. 
	(1) In PyTorch 2.6, we changed the default value of the `weights_only` argument in `torch.load` from `False` to `True`. Re-running `torch.load` with `weights_only` set to `False` will likely succeed, but it can result in arbitrary code execution. Do it only if you got the file from a trusted source.
	(2) Alternatively, to load with `weights_only=True` please check the recommended steps in the following error message.
	WeightsUnpickler error: Unsupported global: GLOBAL argparse.Namespace was not an allowed global by default. Please use `torch.serialization.add_safe_globals([argparse.Namespace])` or the `torch.serialization.safe_globals([argparse.Namespace])` context manager to allowlist this global if you trust this class/function.

Check the documentation of torch.load to learn more about types accepted by default with weights_only https://pytorch.org/docs/stable/generated/torch.load.html.

