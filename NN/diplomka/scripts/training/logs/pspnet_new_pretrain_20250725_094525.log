usage: CNN_main_spheroid.py [-h] --dataset_path DATASET_PATH
                            [--output_dir OUTPUT_DIR]
                            [--model {resunet,resunet_advanced,resunet_optimized,resunet_small,hrnet,pspnet,pspnet_regular,umamba}]
                            [--pretrained] [--epochs EPOCHS]
                            [--batch_size BATCH_SIZE] [--lr LR]
                            [--weight_decay WEIGHT_DECAY]
                            [--img_size IMG_SIZE]
                            [--focal_weight FOCAL_WEIGHT]
                            [--dice_weight DICE_WEIGHT]
                            [--iou_weight IOU_WEIGHT]
                            [--boundary_weight BOUNDARY_WEIGHT]
                            [--aux_weight AUX_WEIGHT]
                            [--optimizer {adam,adamw,sgd}]
                            [--scheduler {reduce,cosine,onecycle}]
                            [--num_workers NUM_WORKERS] [--patience PATIENCE]
                            [--gpus GPUS] [--use_tta] [--use_instance_norm]
                            [--find_lr] [--min_delta MIN_DELTA] [--use_cache]
                            [--pretrained_path PRETRAINED_PATH]
                            [--freeze_backbone_epochs FREEZE_BACKBONE_EPOCHS]
                            [--use_checkpoint]
                            [--gradient_accumulation_steps GRADIENT_ACCUMULATION_STEPS]
                            [--gradient_clip_val GRADIENT_CLIP_VAL]
CNN_main_spheroid.py: error: unrecognized arguments: --min_lr 1e-7 --mixed_precision --warmup_epochs 3 --save_every 5 --validate_every 1 --augment_prob 0.7 --early_stopping_patience 15
