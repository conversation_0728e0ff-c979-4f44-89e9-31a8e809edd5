2025-07-25 09:30:52,375 - INFO - Setting up PSPNet model...
2025-07-25 09:30:53,236 - INFO - Model: PSPNet with ResNet101 backbone
2025-07-25 09:30:53,236 - INFO - Total parameters: 70,293,314 (70.29M)
2025-07-25 09:30:53,236 - INFO - Trainable parameters: 70,293,314 (70.29M)
2025-07-25 09:30:53,393 - INFO - Using 2 GPUs
2025-07-25 09:30:53,393 - INFO - Setting up data loaders...
2025-07-25 09:30:54,111 - INFO - Training samples: 28778
2025-07-25 09:30:54,111 - INFO - Validation samples: 2539
2025-07-25 09:30:54,111 - INFO - Training batches: 4796
2025-07-25 09:30:54,111 - INFO - Validation batches: 424
2025-07-25 09:30:54,111 - INFO - Setting up training components...
/home/<USER>/SpheroSeg/NN/diplomka/train_pspnet_new.py:171: FutureWarning: `torch.cuda.amp.GradScaler(args...)` is deprecated. Please use `torch.amp.GradScaler('cuda', args...)` instead.
  self.scaler = torch.cuda.amp.GradScaler() if self.args.mixed_precision else None
2025-07-25 09:30:54,112 - INFO - Starting PSPNet training...
2025-07-25 09:30:54,112 - INFO - Training for 25 epochs
2025-07-25 09:30:54,112 - INFO - 
Epoch 1/25

TRAIN Dataset Summary:
  Total images found: 28778
  Valid image-mask pairs: 28778
  Images without masks: 0
  Final train dataset size: 28778

VAL Dataset Summary:
  Total images found: 2539
  Valid image-mask pairs: 2539
  Images without masks: 0
  Final val dataset size: 2539
Traceback (most recent call last):
  File "/home/<USER>/SpheroSeg/NN/diplomka/train_pspnet_new.py", line 408, in <module>
    main()
  File "/home/<USER>/SpheroSeg/NN/diplomka/train_pspnet_new.py", line 405, in main
    trainer.train()
  File "/home/<USER>/SpheroSeg/NN/diplomka/train_pspnet_new.py", line 314, in train
    train_loss, train_main_loss, train_aux_loss = self.train_epoch(epoch)
  File "/home/<USER>/SpheroSeg/NN/diplomka/train_pspnet_new.py", line 185, in train_epoch
    for batch_idx, (images, masks, _) in enumerate(self.train_loader):
ValueError: not enough values to unpack (expected 3, got 2)
