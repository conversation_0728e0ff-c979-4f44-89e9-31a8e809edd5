usage: CNN_main_spheroid.py [-h] --dataset_path DATASET_PATH
                            [--output_dir OUTPUT_DIR]
                            [--model {resunet,hrnet,pspnet,transunet}]
                            [--pretrained] [--epochs EPOCHS]
                            [--batch_size BATCH_SIZE] [--lr LR]
                            [--weight_decay WEIGHT_DECAY]
                            [--img_size IMG_SIZE]
                            [--focal_weight FOCAL_WEIGHT]
                            [--dice_weight DICE_WEIGHT]
                            [--iou_weight IOU_WEIGHT]
                            [--boundary_weight BOUNDARY_WEIGHT]
                            [--optimizer {adam,adamw,sgd}]
                            [--scheduler {reduce,cosine,onecycle}]
                            [--num_workers NUM_WORKERS] [--patience PATIENCE]
                            [--gpus GPUS] [--use_tta] [--use_instance_norm]
                            [--find_lr] [--min_delta MIN_DELTA] [--use_cache]
                            [--pretrained_path PRETRAINED_PATH]
                            [--freeze_backbone_epochs FREEZE_BACKBONE_EPOCHS]
CNN_main_spheroid.py: error: unrecognized arguments: --accumulate_grad_batches 2 --use_amp --gradient_checkpointing
