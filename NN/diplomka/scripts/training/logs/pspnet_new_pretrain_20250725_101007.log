============================================================
COMPLETE DATASET SUMMARY
============================================================
Train samples: 28,778
Val samples:   2,539
Test samples:  653
Total samples: 31,970
============================================================

TRAIN Dataset Summary:
  Total images found: 28778
  Valid image-mask pairs: 28778
  Images without masks: 0
  Final train dataset size: 28778

VAL Dataset Summary:
  Total images found: 2539
  Valid image-mask pairs: 2539
  Images without masks: 0
  Final val dataset size: 2539

TEST Dataset Summary:
  Total images found: 653
  Valid image-mask pairs: 653
  Images without masks: 0
  Final test dataset size: 653

============================================================
COMPLETE DATASET SUMMARY
============================================================
Dataset path: /data/prusek/training_big
Image size: 1024x1024
------------------------------------------------------------
Train samples: 28,778
Val samples:   2,539
Test samples:  653
------------------------------------------------------------
Total samples: 31,970
============================================================


Epoch 0 Training:   0%|          | 0/2398 [00:00<?, ?it/s]
TRAIN Dataset Summary:
  Total images found: 28778
  Valid image-mask pairs: 28778
  Images without masks: 0
  Final train dataset size: 28778

VAL Dataset Summary:
  Total images found: 2539
  Valid image-mask pairs: 2539
  Images without masks: 0
  Final val dataset size: 2539
[rank0]:[W725 10:10:39.618765166 reducer.cpp:1430] Warning: find_unused_parameters=True was specified in DDP constructor, but did not find any unused parameters in the forward pass. This flag results in an extra traversal of the autograd graph every iteration,  which can adversely affect performance. If your model indeed never has any unused parameters in the forward pass, consider turning this flag off. Note that this warning may be a false positive if your model has flow control causing later iterations to have unused parameters. (function operator())
[rank1]:[W725 10:10:39.704398093 reducer.cpp:1430] Warning: find_unused_parameters=True was specified in DDP constructor, but did not find any unused parameters in the forward pass. This flag results in an extra traversal of the autograd graph every iteration,  which can adversely affect performance. If your model indeed never has any unused parameters in the forward pass, consider turning this flag off. Note that this warning may be a false positive if your model has flow control causing later iterations to have unused parameters. (function operator())

Epoch 0 Training:   0%|          | 1/2398 [00:24<16:30:31, 24.79s/it]
Epoch 0 Training:   0%|          | 2/2398 [00:25<7:12:17, 10.83s/it] 
Epoch 0 Training:   0%|          | 3/2398 [00:26<4:13:53,  6.36s/it]
Epoch 0 Training:   0%|          | 4/2398 [00:27<2:50:03,  4.26s/it]
Epoch 0 Training:   0%|          | 5/2398 [00:28<2:03:42,  3.10s/it]
Epoch 0 Training:   0%|          | 6/2398 [00:30<1:35:49,  2.40s/it]
Epoch 0 Training:   0%|          | 7/2398 [00:31<1:18:06,  1.96s/it]
Epoch 0 Training:   0%|          | 8/2398 [00:32<1:06:31,  1.67s/it]
Epoch 0 Training:   0%|          | 8/2398 [00:34<2:50:03,  4.27s/it]
NaN/Inf gradients detected at batch 0, skipping optimizer step
NaN/Inf gradients detected at batch 1, skipping optimizer step
NaN/Inf gradients detected at batch 2, skipping optimizer step
NaN/Inf gradients detected at batch 3, skipping optimizer step
NaN/Inf gradients detected at batch 4, skipping optimizer step
NaN/Inf gradients detected at batch 5, skipping optimizer step
NaN/Inf gradients detected at batch 6, skipping optimizer step
NaN/Inf gradients detected at batch 7, skipping optimizer step
WARNING: Very large gradient norm: 1801.6273, consider reducing LR
NaN/Inf gradients detected at batch 0, skipping optimizer step
NaN/Inf gradients detected at batch 1, skipping optimizer step
NaN/Inf gradients detected at batch 2, skipping optimizer step
NaN/Inf gradients detected at batch 3, skipping optimizer step
NaN/Inf gradients detected at batch 4, skipping optimizer step
NaN/Inf gradients detected at batch 5, skipping optimizer step
NaN/Inf gradients detected at batch 6, skipping optimizer step
NaN/Inf gradients detected at batch 7, skipping optimizer step
WARNING: Very large gradient norm: 1801.6273, consider reducing LR
[rank0]:[W725 10:10:50.364333576 ProcessGroupNCCL.cpp:1479] Warning: WARNING: destroy_process_group() was not called before program exit, which can leak resources. For more info, please see https://pytorch.org/docs/stable/distributed.html#shutdown (function operator())
W0725 10:10:51.190347 2424828 torch/multiprocessing/spawn.py:169] Terminating process 2424995 via signal SIGTERM
Traceback (most recent call last):
  File "/home/<USER>/SpheroSeg/NN/diplomka/CNN_main_spheroid.py", line 1422, in <module>
    main()
  File "/home/<USER>/SpheroSeg/NN/diplomka/CNN_main_spheroid.py", line 1417, in main
    mp.spawn(train, args=(world_size, args), nprocs=world_size, join=True)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 340, in spawn
    return start_processes(fn, args, nprocs, join, daemon, start_method="spawn")
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 296, in start_processes
    while not context.join():
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 215, in join
    raise ProcessRaisedException(msg, error_index, failed_process.pid)
torch.multiprocessing.spawn.ProcessRaisedException: 

-- Process 0 terminated with the following error:
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 90, in _wrap
    fn(i, *args)
  File "/home/<USER>/SpheroSeg/NN/diplomka/CNN_main_spheroid.py", line 1251, in train
    train_loss, train_metrics, loss_components = train_epoch(
  File "/home/<USER>/SpheroSeg/NN/diplomka/CNN_main_spheroid.py", line 539, in train_epoch
    loss_components[k] += v
KeyError: 'aux_focal'

