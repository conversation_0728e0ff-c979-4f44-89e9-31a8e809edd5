============================================================
COMPLETE DATASET SUMMARY
============================================================
Train samples: 28,778
Val samples:   2,539
Test samples:  653
Total samples: 31,970
============================================================

TRAIN Dataset Summary:
  Total images found: 28778
  Valid image-mask pairs: 28778
  Images without masks: 0
  Final train dataset size: 28778

VAL Dataset Summary:
  Total images found: 2539
  Valid image-mask pairs: 2539
  Images without masks: 0
  Final val dataset size: 2539

TRAIN Dataset Summary:
  Total images found: 28778
  Valid image-mask pairs: 28778
  Images without masks: 0
  Final train dataset size: 28778

VAL Dataset Summary:
  Total images found: 2539
  Valid image-mask pairs: 2539
  Images without masks: 0
  Final val dataset size: 2539

TEST Dataset Summary:
  Total images found: 653
  Valid image-mask pairs: 653
  Images without masks: 0
  Final test dataset size: 653

============================================================
COMPLETE DATASET SUMMARY
============================================================
Dataset path: /data/prusek/training_big
Image size: 1024x1024
------------------------------------------------------------
Train samples: 28,778
Val samples:   2,539
Test samples:  653
------------------------------------------------------------
Total samples: 31,970
============================================================


Epoch 0 Training:   0%|          | 0/7194 [00:00<?, ?it/s]
Epoch 0 Training:   0%|          | 0/7194 [00:19<?, ?it/s]
[rank0]:[W713 19:17:41.935294101 ProcessGroupNCCL.cpp:1479] Warning: WARNING: destroy_process_group() was not called before program exit, which can leak resources. For more info, please see https://pytorch.org/docs/stable/distributed.html#shutdown (function operator())
[rank1]:[W713 19:17:42.703072661 TCPStore.cpp:125] [c10d] recvValue failed on SocketImpl(fd=8, addr=[localhost]:39094, remote=[localhost]:12355): failed to recv, got 0 bytes
Exception raised from recvBytes at /pytorch/torch/csrc/distributed/c10d/Utils.hpp:678 (most recent call first):
frame #0: c10::Error::Error(c10::SourceLocation, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >) + 0x98 (0x7f96ae5cf5e8 in /home/<USER>/.local/lib/python3.9/site-packages/torch/lib/libc10.so)
frame #1: <unknown function> + 0x5ba8bfe (0x7f955fb58bfe in /home/<USER>/.local/lib/python3.9/site-packages/torch/lib/libtorch_cpu.so)
frame #2: <unknown function> + 0x5baaf40 (0x7f955fb5af40 in /home/<USER>/.local/lib/python3.9/site-packages/torch/lib/libtorch_cpu.so)
frame #3: <unknown function> + 0x5bab84a (0x7f955fb5b84a in /home/<USER>/.local/lib/python3.9/site-packages/torch/lib/libtorch_cpu.so)
frame #4: c10d::TCPStore::check(std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&) + 0x2a9 (0x7f955fb552a9 in /home/<USER>/.local/lib/python3.9/site-packages/torch/lib/libtorch_cpu.so)
frame #5: c10d::ProcessGroupNCCL::heartbeatMonitor() + 0x379 (0x7f95212539f9 in /home/<USER>/.local/lib/python3.9/site-packages/torch/lib/libtorch_cuda.so)
frame #6: <unknown function> + 0xdbad4 (0x7f96b90dbad4 in /lib64/libstdc++.so.6)
frame #7: <unknown function> + 0x8a19a (0x7f96c988a19a in /lib64/libc.so.6)
frame #8: <unknown function> + 0x10f210 (0x7f96c990f210 in /lib64/libc.so.6)

[rank1]:[W713 19:17:42.706642219 ProcessGroupNCCL.cpp:1662] [PG ID 0 PG GUID 0(default_pg) Rank 1] Failed to check the "should dump" flag on TCPStore, (maybe TCPStore server has shut down too early), with error: failed to recv, got 0 bytes
W0713 19:17:43.271518 2779718 torch/multiprocessing/spawn.py:169] Terminating process 2779849 via signal SIGTERM
Traceback (most recent call last):
  File "/home/<USER>/SpheroSeg/NN/diplomka/CNN_main_spheroid.py", line 1229, in <module>
    main()
  File "/home/<USER>/SpheroSeg/NN/diplomka/CNN_main_spheroid.py", line 1224, in main
    mp.spawn(train, args=(world_size, args), nprocs=world_size, join=True)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 340, in spawn
    return start_processes(fn, args, nprocs, join, daemon, start_method="spawn")
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 296, in start_processes
    while not context.join():
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 215, in join
    raise ProcessRaisedException(msg, error_index, failed_process.pid)
torch.multiprocessing.spawn.ProcessRaisedException: 

-- Process 0 terminated with the following error:
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 90, in _wrap
    fn(i, *args)
  File "/home/<USER>/SpheroSeg/NN/diplomka/CNN_main_spheroid.py", line 1069, in train
    train_loss, train_metrics, loss_components = train_epoch(
  File "/home/<USER>/SpheroSeg/NN/diplomka/CNN_main_spheroid.py", line 416, in train_epoch
    outputs = model(images)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/nn/parallel/distributed.py", line 1637, in forward
    else self._run_ddp_forward(*inputs, **kwargs)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/nn/parallel/distributed.py", line 1464, in _run_ddp_forward
    return self.module(*inputs, **kwargs)  # type: ignore[index]
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/SpheroSeg/NN/diplomka/models/transunet.py", line 325, in forward
    x = self.decoder4(x, skip_features[3])
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/SpheroSeg/NN/diplomka/models/transunet.py", line 235, in forward
    x = torch.cat([x, skip], dim=1)
RuntimeError: Sizes of tensors must match except in dimension 1. Expected size 128 but got size 64 for tensor number 1 in the list.

