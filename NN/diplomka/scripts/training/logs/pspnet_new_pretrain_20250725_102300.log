============================================================
COMPLETE DATASET SUMMARY
============================================================
Train samples: 28,778
Val samples:   2,539
Test samples:  653
Total samples: 31,970
============================================================

TRAIN Dataset Summary:
  Total images found: 28778
  Valid image-mask pairs: 28778
  Images without masks: 0
  Final train dataset size: 28778

VAL Dataset Summary:
  Total images found: 2539
  Valid image-mask pairs: 2539
  Images without masks: 0
  Final val dataset size: 2539

TEST Dataset Summary:
  Total images found: 653
  Valid image-mask pairs: 653
  Images without masks: 0
  Final test dataset size: 653

============================================================
COMPLETE DATASET SUMMARY
============================================================
Dataset path: /data/prusek/training_big
Image size: 1024x1024
------------------------------------------------------------
Train samples: 28,778
Val samples:   2,539
Test samples:  653
------------------------------------------------------------
Total samples: 31,970
============================================================


Epoch 0 Training:   0%|          | 0/2398 [00:00<?, ?it/s]
TRAIN Dataset Summary:
  Total images found: 28778
  Valid image-mask pairs: 28778
  Images without masks: 0
  Final train dataset size: 28778

VAL Dataset Summary:
  Total images found: 2539
  Valid image-mask pairs: 2539
  Images without masks: 0
  Final val dataset size: 2539
[rank0]:[W725 10:23:33.657896096 reducer.cpp:1430] Warning: find_unused_parameters=True was specified in DDP constructor, but did not find any unused parameters in the forward pass. This flag results in an extra traversal of the autograd graph every iteration,  which can adversely affect performance. If your model indeed never has any unused parameters in the forward pass, consider turning this flag off. Note that this warning may be a false positive if your model has flow control causing later iterations to have unused parameters. (function operator())
[rank1]:[W725 10:23:34.068283158 reducer.cpp:1430] Warning: find_unused_parameters=True was specified in DDP constructor, but did not find any unused parameters in the forward pass. This flag results in an extra traversal of the autograd graph every iteration,  which can adversely affect performance. If your model indeed never has any unused parameters in the forward pass, consider turning this flag off. Note that this warning may be a false positive if your model has flow control causing later iterations to have unused parameters. (function operator())

Epoch 0 Training:   0%|          | 1/2398 [00:25<16:49:56, 25.28s/it]
Epoch 0 Training:   0%|          | 2/2398 [00:26<7:20:15, 11.02s/it] 
Epoch 0 Training:   0%|          | 3/2398 [00:27<4:18:13,  6.47s/it]
Epoch 0 Training:   0%|          | 4/2398 [00:28<2:52:42,  4.33s/it]
Epoch 0 Training:   0%|          | 5/2398 [00:29<2:05:25,  3.14s/it]
Epoch 0 Training:   0%|          | 6/2398 [00:30<1:36:54,  2.43s/it]
Epoch 0 Training:   0%|          | 7/2398 [00:31<1:18:54,  1.98s/it]
Epoch 0 Training:   0%|          | 8/2398 [00:32<1:07:01,  1.68s/it]
Epoch 0 Training:   0%|          | 9/2398 [00:33<59:08,  1.49s/it]  
Epoch 0 Training:   0%|          | 9/2398 [00:34<59:08,  1.49s/it, loss=24.8, iou=0.231, dice=0.375]
Epoch 0 Training:   0%|          | 10/2398 [00:34<54:38,  1.37s/it, loss=24.8, iou=0.231, dice=0.375]
Epoch 0 Training:   0%|          | 10/2398 [00:35<54:38,  1.37s/it, loss=26.3, iou=0.118, dice=0.211]
Epoch 0 Training:   0%|          | 11/2398 [00:35<50:56,  1.28s/it, loss=26.3, iou=0.118, dice=0.211]
Epoch 0 Training:   0%|          | 11/2398 [00:36<50:56,  1.28s/it, loss=22.3, iou=0.131, dice=0.232]
Epoch 0 Training:   1%|          | 12/2398 [00:36<48:23,  1.22s/it, loss=22.3, iou=0.131, dice=0.232]
Epoch 0 Training:   1%|          | 12/2398 [00:38<48:23,  1.22s/it, loss=28.6, iou=0.202, dice=0.336]
Epoch 0 Training:   1%|          | 13/2398 [00:38<46:42,  1.17s/it, loss=28.6, iou=0.202, dice=0.336]
Epoch 0 Training:   1%|          | 13/2398 [00:39<46:42,  1.17s/it, loss=27.1, iou=0.223, dice=0.365]
Epoch 0 Training:   1%|          | 14/2398 [00:39<45:31,  1.15s/it, loss=27.1, iou=0.223, dice=0.365]
Epoch 0 Training:   1%|          | 14/2398 [00:40<45:31,  1.15s/it, loss=27.2, iou=0.21, dice=0.347] 
Epoch 0 Training:   1%|          | 15/2398 [00:40<44:38,  1.12s/it, loss=27.2, iou=0.21, dice=0.347]
Epoch 0 Training:   1%|          | 15/2398 [00:41<44:38,  1.12s/it, loss=26.9, iou=0.166, dice=0.285]
Epoch 0 Training:   1%|          | 16/2398 [00:41<44:01,  1.11s/it, loss=26.9, iou=0.166, dice=0.285]
Epoch 0 Training:   1%|          | 16/2398 [00:42<44:01,  1.11s/it, loss=28.7, iou=0.216, dice=0.355]
Epoch 0 Training:   1%|          | 17/2398 [00:42<43:33,  1.10s/it, loss=28.7, iou=0.216, dice=0.355]
Epoch 0 Training:   1%|          | 17/2398 [00:43<43:33,  1.10s/it, loss=26.3, iou=0.203, dice=0.337]
Epoch 0 Training:   1%|          | 18/2398 [00:43<43:15,  1.09s/it, loss=26.3, iou=0.203, dice=0.337]
Epoch 0 Training:   1%|          | 18/2398 [00:44<43:15,  1.09s/it, loss=23.5, iou=0.149, dice=0.26] 
Epoch 0 Training:   1%|          | 19/2398 [00:44<43:01,  1.09s/it, loss=23.5, iou=0.149, dice=0.26]
Epoch 0 Training:   1%|          | 19/2398 [00:45<43:01,  1.09s/it, loss=21.4, iou=0.191, dice=0.321]
Epoch 0 Training:   1%|          | 20/2398 [00:45<42:51,  1.08s/it, loss=21.4, iou=0.191, dice=0.321]
Epoch 0 Training:   1%|          | 20/2398 [00:46<42:51,  1.08s/it, loss=17.8, iou=0.164, dice=0.282]
Epoch 0 Training:   1%|          | 21/2398 [00:46<42:45,  1.08s/it, loss=17.8, iou=0.164, dice=0.282]
Epoch 0 Training:   1%|          | 21/2398 [00:47<42:45,  1.08s/it, loss=18.5, iou=0.171, dice=0.292]
Epoch 0 Training:   1%|          | 22/2398 [00:47<42:40,  1.08s/it, loss=18.5, iou=0.171, dice=0.292]
Epoch 0 Training:   1%|          | 22/2398 [00:48<42:40,  1.08s/it, loss=15.4, iou=0.173, dice=0.295]
Epoch 0 Training:   1%|          | 23/2398 [00:48<42:35,  1.08s/it, loss=15.4, iou=0.173, dice=0.295]
Epoch 0 Training:   1%|          | 23/2398 [00:49<42:35,  1.08s/it, loss=18, iou=0.167, dice=0.287]  
Epoch 0 Training:   1%|          | 24/2398 [00:49<42:32,  1.08s/it, loss=18, iou=0.167, dice=0.287]
Epoch 0 Training:   1%|          | 24/2398 [00:50<42:32,  1.08s/it, loss=17.9, iou=0.345, dice=0.513]
Epoch 0 Training:   1%|          | 25/2398 [00:50<42:31,  1.08s/it, loss=17.9, iou=0.345, dice=0.513]
Epoch 0 Training:   1%|          | 25/2398 [00:51<42:31,  1.08s/it, loss=19.6, iou=0.27, dice=0.426] 
Epoch 0 Training:   1%|          | 26/2398 [00:51<42:30,  1.08s/it, loss=19.6, iou=0.27, dice=0.426]
Epoch 0 Training:   1%|          | 26/2398 [00:53<42:30,  1.08s/it, loss=20.7, iou=0.14, dice=0.246]
Epoch 0 Training:   1%|          | 27/2398 [00:53<42:29,  1.08s/it, loss=20.7, iou=0.14, dice=0.246]
Epoch 0 Training:   1%|          | 27/2398 [00:54<42:29,  1.08s/it, loss=13.8, iou=0.384, dice=0.554]
Epoch 0 Training:   1%|          | 28/2398 [00:54<42:29,  1.08s/it, loss=13.8, iou=0.384, dice=0.554]
Epoch 0 Training:   1%|          | 28/2398 [00:55<42:29,  1.08s/it, loss=11.9, iou=0.298, dice=0.459]
Epoch 0 Training:   1%|          | 29/2398 [00:55<42:27,  1.08s/it, loss=11.9, iou=0.298, dice=0.459]
Epoch 0 Training:   1%|          | 29/2398 [00:56<42:27,  1.08s/it, loss=18.2, iou=0.416, dice=0.587]
Epoch 0 Training:   1%|▏         | 30/2398 [00:56<42:24,  1.07s/it, loss=18.2, iou=0.416, dice=0.587]
Epoch 0 Training:   1%|▏         | 30/2398 [00:57<42:24,  1.07s/it, loss=21.3, iou=0.226, dice=0.369]
Epoch 0 Training:   1%|▏         | 31/2398 [00:57<42:26,  1.08s/it, loss=21.3, iou=0.226, dice=0.369]
Epoch 0 Training:   1%|▏         | 31/2398 [00:58<42:26,  1.08s/it, loss=14.9, iou=0.235, dice=0.381]
Epoch 0 Training:   1%|▏         | 32/2398 [00:58<42:24,  1.08s/it, loss=14.9, iou=0.235, dice=0.381]
Epoch 0 Training:   1%|▏         | 32/2398 [00:59<42:24,  1.08s/it, loss=21, iou=0.232, dice=0.377]  
Epoch 0 Training:   1%|▏         | 33/2398 [00:59<42:22,  1.07s/it, loss=21, iou=0.232, dice=0.377]
Epoch 0 Training:   1%|▏         | 33/2398 [01:00<42:22,  1.07s/it, loss=21.6, iou=0.201, dice=0.335]
Epoch 0 Training:   1%|▏         | 34/2398 [01:00<42:21,  1.08s/it, loss=21.6, iou=0.201, dice=0.335]
Epoch 0 Training:   1%|▏         | 34/2398 [01:01<42:21,  1.08s/it, loss=15.4, iou=0.217, dice=0.356]
Epoch 0 Training:   1%|▏         | 35/2398 [01:01<42:18,  1.07s/it, loss=15.4, iou=0.217, dice=0.356]
Epoch 0 Training:   1%|▏         | 35/2398 [01:02<42:18,  1.07s/it, loss=14.7, iou=0.338, dice=0.505]
Epoch 0 Training:   2%|▏         | 36/2398 [01:02<42:17,  1.07s/it, loss=14.7, iou=0.338, dice=0.505]
Epoch 0 Training:   2%|▏         | 36/2398 [01:03<42:17,  1.07s/it, loss=8.02, iou=0.35, dice=0.519] 
Epoch 0 Training:   2%|▏         | 37/2398 [01:03<42:16,  1.07s/it, loss=8.02, iou=0.35, dice=0.519]
Epoch 0 Training:   2%|▏         | 37/2398 [01:04<42:16,  1.07s/it, loss=7.42, iou=0.234, dice=0.38]
Epoch 0 Training:   2%|▏         | 38/2398 [01:04<42:14,  1.07s/it, loss=7.42, iou=0.234, dice=0.38]
Epoch 0 Training:   2%|▏         | 38/2398 [01:05<42:14,  1.07s/it, loss=6.34, iou=0.508, dice=0.674]
Epoch 0 Training:   2%|▏         | 39/2398 [01:05<42:12,  1.07s/it, loss=6.34, iou=0.508, dice=0.674]
Epoch 0 Training:   2%|▏         | 39/2398 [01:07<42:12,  1.07s/it, loss=5.86, iou=0.398, dice=0.569]
Epoch 0 Training:   2%|▏         | 40/2398 [01:07<42:13,  1.07s/it, loss=5.86, iou=0.398, dice=0.569]
Epoch 0 Training:   2%|▏         | 40/2398 [01:08<42:13,  1.07s/it, loss=8.17, iou=0.269, dice=0.424]
Epoch 0 Training:   2%|▏         | 41/2398 [01:08<42:11,  1.07s/it, loss=8.17, iou=0.269, dice=0.424]
Epoch 0 Training:   2%|▏         | 41/2398 [01:09<42:11,  1.07s/it, loss=5.86, iou=0.355, dice=0.524]
Epoch 0 Training:   2%|▏         | 42/2398 [01:09<42:08,  1.07s/it, loss=5.86, iou=0.355, dice=0.524]
Epoch 0 Training:   2%|▏         | 42/2398 [01:10<42:08,  1.07s/it, loss=10.3, iou=0.365, dice=0.534]
Epoch 0 Training:   2%|▏         | 43/2398 [01:10<42:06,  1.07s/it, loss=10.3, iou=0.365, dice=0.534]
Epoch 0 Training:   2%|▏         | 43/2398 [01:11<42:06,  1.07s/it, loss=14.1, iou=0.202, dice=0.335]
Epoch 0 Training:   2%|▏         | 44/2398 [01:11<42:05,  1.07s/it, loss=14.1, iou=0.202, dice=0.335]
Epoch 0 Training:   2%|▏         | 44/2398 [01:12<42:05,  1.07s/it, loss=15.9, iou=0.219, dice=0.359]
Epoch 0 Training:   2%|▏         | 45/2398 [01:12<42:04,  1.07s/it, loss=15.9, iou=0.219, dice=0.359]
Epoch 0 Training:   2%|▏         | 45/2398 [01:13<42:04,  1.07s/it, loss=8.73, iou=0.253, dice=0.404]
Epoch 0 Training:   2%|▏         | 46/2398 [01:13<42:02,  1.07s/it, loss=8.73, iou=0.253, dice=0.404]
Epoch 0 Training:   2%|▏         | 46/2398 [01:14<42:02,  1.07s/it, loss=12.3, iou=0.222, dice=0.364]
Epoch 0 Training:   2%|▏         | 47/2398 [01:14<42:01,  1.07s/it, loss=12.3, iou=0.222, dice=0.364]
Epoch 0 Training:   2%|▏         | 47/2398 [01:15<42:01,  1.07s/it, loss=7.31, iou=0.37, dice=0.54]  
Epoch 0 Training:   2%|▏         | 48/2398 [01:15<42:00,  1.07s/it, loss=7.31, iou=0.37, dice=0.54]
Epoch 0 Training:   2%|▏         | 48/2398 [01:16<42:00,  1.07s/it, loss=9.17, iou=0.505, dice=0.671]
Epoch 0 Training:   2%|▏         | 49/2398 [01:16<42:01,  1.07s/it, loss=9.17, iou=0.505, dice=0.671]
Epoch 0 Training:   2%|▏         | 49/2398 [01:17<42:01,  1.07s/it, loss=9.32, iou=0.454, dice=0.625]
Epoch 0 Training:   2%|▏         | 50/2398 [01:17<41:59,  1.07s/it, loss=9.32, iou=0.454, dice=0.625]