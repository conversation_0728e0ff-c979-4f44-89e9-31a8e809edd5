#!/bin/bash
# Validate dataset before training

DATASET_PATH="${1:-/Volumes/T7/Datasets/sféroidy/all/training_big}"

echo "=========================================="
echo "Dataset Validation"
echo "=========================================="
echo "Dataset path: $DATASET_PATH"
echo ""

# Check if dataset exists
if [ ! -d "$DATASET_PATH" ]; then
    echo "ERROR: Dataset path does not exist!"
    exit 1
fi

# Run format check
echo "1. Checking dataset formats and pairs..."
echo "------------------------------------------"
python scripts/validation/check_dataset_formats.py --dataset_path "$DATASET_PATH"

echo ""
echo "2. Testing dataset loading..."
echo "------------------------------------------"
python scripts/validation/test_dataset_loading.py --dataset_path "$DATASET_PATH"

echo ""
echo "=========================================="
echo "Validation complete!"
echo "=========================================="
echo ""
echo "If all checks passed, you can start training with:"
echo "  bash train_hrnet.sh"
echo "  bash train_transunet.sh"
echo "  bash train_resunet.sh"
echo "  bash train_pspnet.sh"