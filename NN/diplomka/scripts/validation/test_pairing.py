#!/usr/bin/env python3
"""
Test that dataset can properly pair images with masks, including .ome.tiff -> .tiff matching
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from pathlib import Path
import argparse
from CNN_main_spheroid import CachedSpheroidDataset

def test_dataset_pairing(dataset_path):
    """Test that dataset can properly load and pair images with masks"""
    dataset_path = Path(dataset_path)
    
    print("Testing dataset pairing...")
    print(f"Dataset path: {dataset_path}")
    print("="*60)
    
    # Test if dataset has train/val/test structure or simple images/masks
    if (dataset_path / 'train').exists():
        # Standard train/val/test structure
        splits = ['train', 'val', 'test']
        for split in splits:
            if (dataset_path / split).exists():
                print(f"\nTesting {split} split...")
                dataset = CachedSpheroidDataset(
                    dataset_dir=dataset_path,
                    split=split,
                    transform=None,
                    use_cache=False
                )
                
                if len(dataset) > 0:
                    # Try to load first few samples
                    print(f"  Trying to load first 3 samples...")
                    for i in range(min(3, len(dataset))):
                        try:
                            img, mask = dataset[i]
                            img_path, mask_path = dataset.valid_files[i]
                            print(f"  Sample {i}: OK")
                            print(f"    Image: {img_path.name}")
                            print(f"    Mask: {mask_path.name}")
                            print(f"    Image shape: {img.shape}")
                            print(f"    Mask shape: {mask.shape}")
                        except Exception as e:
                            print(f"  Sample {i}: FAILED - {e}")
    else:
        # Simple images/masks structure
        print("\nDataset has simple images/masks structure")
        
        # Create fake dataset structure for testing
        import tempfile
        with tempfile.TemporaryDirectory() as tmpdir:
            tmpdir = Path(tmpdir)
            
            # Create train split with symlinks
            train_dir = tmpdir / 'train'
            (train_dir / 'images').mkdir(parents=True)
            (train_dir / 'masks').mkdir(parents=True)
            
            # Copy structure instead of symlink
            (train_dir / 'images').rmdir()
            (train_dir / 'masks').rmdir()
            
            # Create symlinks to actual directories
            (train_dir / 'images').symlink_to(dataset_path / 'images')
            (train_dir / 'masks').symlink_to(dataset_path / 'masks')
            
            dataset = CachedSpheroidDataset(
                dataset_dir=tmpdir,
                split='train',
                transform=None,
                use_cache=False
            )
            
            if len(dataset) > 0:
                # Try to load first few samples
                print(f"  Trying to load first 3 samples...")
                for i in range(min(3, len(dataset))):
                    try:
                        img, mask = dataset[i]
                        img_path, mask_path = dataset.valid_files[i]
                        print(f"  Sample {i}: OK")
                        print(f"    Image: {img_path.name}")
                        print(f"    Mask: {mask_path.name}")
                        print(f"    Image shape: {img.shape}")
                        print(f"    Mask shape: {mask.shape}")
                    except Exception as e:
                        print(f"  Sample {i}: FAILED - {e}")
    
    print("\n" + "="*60)
    print("Testing complete!")

def main():
    parser = argparse.ArgumentParser(description='Test dataset pairing')
    parser.add_argument('--dataset_path', type=str, required=True,
                       help='Path to dataset directory')
    args = parser.parse_args()
    
    test_dataset_pairing(args.dataset_path)

if __name__ == '__main__':
    main()