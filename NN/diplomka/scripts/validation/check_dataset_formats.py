#!/usr/bin/env python3
"""
Check dataset for various image formats and validate image-mask pairs
"""

import argparse
from pathlib import Path
from collections import defaultdict
import cv2 as cv

def check_dataset(dataset_path):
    """Check dataset for various formats and validate pairs"""
    dataset_path = Path(dataset_path)
    
    # Supported formats
    image_extensions = ['*.png', '*.jpg', '*.jpeg', '*.tif', '*.tiff', '*.bmp']
    
    # Check each split
    for split in ['train', 'val', 'test']:
        print(f"\n{'='*60}")
        print(f"Checking {split.upper()} split")
        print('='*60)
        
        split_dir = dataset_path / split
        if not split_dir.exists():
            print(f"WARNING: {split} directory not found!")
            continue
            
        image_dir = split_dir / 'images'
        mask_dir = split_dir / 'masks'
        
        if not image_dir.exists():
            print(f"WARNING: {image_dir} not found!")
            continue
        if not mask_dir.exists():
            print(f"WARNING: {mask_dir} not found!")
            continue
        
        # Count images by format
        format_counts = defaultdict(int)
        all_images = []
        
        for ext in image_extensions:
            images = list(image_dir.glob(ext))
            images.extend(list(image_dir.glob(ext.upper())))
            for img in images:
                if not img.name.startswith('._'):
                    format_counts[img.suffix.lower()] += 1
                    all_images.append(img)
        
        # Remove duplicates
        all_images = sorted(list(set(all_images)))
        
        print(f"\nImage formats found:")
        for fmt, count in sorted(format_counts.items()):
            print(f"  {fmt}: {count} files")
        print(f"  Total: {len(all_images)} images")
        
        # Check for corresponding masks
        valid_pairs = 0
        missing_masks = []
        format_mismatches = []
        
        for img_path in all_images:
            img_stem = img_path.stem
            mask_found = False
            
            # Try to find mask with any extension
            for ext in image_extensions:
                mask_name = img_stem + ext[1:]  # Remove *
                mask_path = mask_dir / mask_name
                if mask_path.exists():
                    valid_pairs += 1
                    mask_found = True
                    
                    # Check if formats match
                    if img_path.suffix.lower() != mask_path.suffix.lower():
                        format_mismatches.append((img_path.name, mask_path.name))
                    break
                    
                # Check uppercase
                mask_name_upper = img_stem + ext[1:].upper()
                mask_path_upper = mask_dir / mask_name_upper
                if mask_path_upper.exists():
                    valid_pairs += 1
                    mask_found = True
                    
                    if img_path.suffix.lower() != mask_path_upper.suffix.lower():
                        format_mismatches.append((img_path.name, mask_path_upper.name))
                    break
            
            if not mask_found:
                missing_masks.append(img_path.name)
        
        print(f"\nPairing results:")
        print(f"  Valid image-mask pairs: {valid_pairs}")
        print(f"  Images without masks: {len(missing_masks)}")
        
        if missing_masks:
            print(f"\n  First 5 images without masks:")
            for img in missing_masks[:5]:
                print(f"    - {img}")
            if len(missing_masks) > 5:
                print(f"    ... and {len(missing_masks) - 5} more")
        
        if format_mismatches:
            print(f"\n  Format mismatches (image uses different format than mask):")
            for img, mask in format_mismatches[:3]:
                print(f"    - {img} <-> {mask}")
            if len(format_mismatches) > 3:
                print(f"    ... and {len(format_mismatches) - 3} more")
        
        # Try to load a sample image-mask pair
        if valid_pairs > 0:
            print(f"\nTesting image loading:")
            # Find first valid pair
            for img_path in all_images[:5]:
                img_stem = img_path.stem
                for ext in image_extensions:
                    mask_name = img_stem + ext[1:]
                    mask_path = mask_dir / mask_name
                    if mask_path.exists():
                        # Try loading
                        img = cv.imread(str(img_path))
                        mask = cv.imread(str(mask_path), cv.IMREAD_GRAYSCALE)
                        
                        if img is not None and mask is not None:
                            print(f"  ✓ Successfully loaded: {img_path.name}")
                            print(f"    Image shape: {img.shape}")
                            print(f"    Mask shape: {mask.shape}")
                            print(f"    Mask unique values: {sorted(set(mask.flatten()))}")
                        else:
                            print(f"  ✗ Failed to load: {img_path.name}")
                        break
                break
    
    # Final summary
    print(f"\n{'='*60}")
    print("SUMMARY")
    print('='*60)
    print("Dataset supports multiple image formats: PNG, JPG, JPEG, TIF, TIFF, BMP")
    print("Images without corresponding masks will be automatically excluded during training")
    print("Format mismatches between images and masks are handled correctly")

def main():
    parser = argparse.ArgumentParser(description='Check dataset formats and validate pairs')
    parser.add_argument('--dataset_path', type=str, required=True,
                       help='Path to dataset (e.g., training_big)')
    args = parser.parse_args()
    
    check_dataset(args.dataset_path)

if __name__ == '__main__':
    main()