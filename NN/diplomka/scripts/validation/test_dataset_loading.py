#!/usr/bin/env python3
"""
Test dataset loading with various formats
"""

import sys
import torch
from pathlib import Path
import matplotlib.pyplot as plt
import numpy as np

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent.parent))

from CNN_main_spheroid import SpheroidDataset, get_validation_augmentation

def test_dataset(dataset_path):
    """Test loading dataset"""
    print("Testing SpheroidDataset loading...")
    
    # Create datasets for all splits
    for split in ['train', 'val', 'test']:
        print(f"\n{'='*50}")
        print(f"Testing {split} split")
        print('='*50)
        
        try:
            dataset = SpheroidDataset(
                dataset_path,
                split=split,
                transform=get_validation_augmentation(512)
            )
            
            if len(dataset) > 0:
                # Try loading first few samples
                print(f"\nLoading first 3 samples...")
                for i in range(min(3, len(dataset))):
                    try:
                        img, mask = dataset[i]
                        print(f"  Sample {i}: Image shape: {img.shape}, Mask shape: {mask.shape}")
                        print(f"    Image dtype: {img.dtype}, range: [{img.min():.2f}, {img.max():.2f}]")
                        print(f"    Mask unique values: {torch.unique(mask).tolist()}")
                    except Exception as e:
                        print(f"  Error loading sample {i}: {e}")
                
                # Create a visualization
                if split == 'train' and len(dataset) > 0:
                    print(f"\nCreating visualization...")
                    fig, axes = plt.subplots(2, 3, figsize=(12, 8))
                    
                    for i in range(min(3, len(dataset))):
                        img, mask = dataset[i]
                        
                        # Convert from tensor to numpy and denormalize
                        img_np = img.permute(1, 2, 0).numpy()
                        mean = np.array([0.485, 0.456, 0.406])
                        std = np.array([0.229, 0.224, 0.225])
                        img_np = std * img_np + mean
                        img_np = np.clip(img_np, 0, 1)
                        
                        mask_np = mask.numpy()
                        
                        # Plot
                        axes[0, i].imshow(img_np)
                        axes[0, i].set_title(f'Image {i}')
                        axes[0, i].axis('off')
                        
                        axes[1, i].imshow(mask_np, cmap='gray')
                        axes[1, i].set_title(f'Mask {i}')
                        axes[1, i].axis('off')
                    
                    plt.tight_layout()
                    plt.savefig('dataset_samples.png', dpi=150)
                    print(f"  Saved visualization to dataset_samples.png")
                    plt.close()
                    
        except Exception as e:
            print(f"Error creating dataset: {e}")
    
    print("\n" + "="*50)
    print("Dataset loading test completed!")
    print("="*50)

def main():
    import argparse
    parser = argparse.ArgumentParser(description='Test dataset loading')
    parser.add_argument('--dataset_path', type=str, required=True,
                       help='Path to dataset')
    args = parser.parse_args()
    
    test_dataset(args.dataset_path)

if __name__ == '__main__':
    main()