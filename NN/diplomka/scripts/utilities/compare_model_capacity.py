# compare_model_capacity.py - Compare model parameters and capacity

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

import torch
import torch.nn as nn
from models.resunet import ResUNet
from models.hrnet import HRNetV2
from models.pspnet import PSPNet
from models.transunet import TransUNet
from prettytable import PrettyTable
import numpy as np

def count_parameters(model):
    """Count trainable and total parameters"""
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    return trainable_params, total_params

def get_model_flops(model, input_size=(1, 3, 1024, 1024)):
    """Estimate FLOPs using forward pass hooks"""
    flops = 0
    
    def hook(module, input, output):
        nonlocal flops
        if isinstance(module, nn.Conv2d):
            # FLOPs = kernel_ops × output_size
            batch_size = input[0].shape[0]
            in_channels = input[0].shape[1]
            input_height = input[0].shape[2]
            input_width = input[0].shape[3]
            kernel_height = module.kernel_size[0]
            kernel_width = module.kernel_size[1]
            out_channels = output.shape[1]
            output_height = output.shape[2]
            output_width = output.shape[3]
            
            kernel_ops = kernel_height * kernel_width * in_channels // module.groups
            output_size = batch_size * out_channels * output_height * output_width
            flops += kernel_ops * output_size
            
        elif isinstance(module, nn.Linear):
            # FLOPs = 2 × input_features × output_features
            batch_size = input[0].shape[0]
            flops += batch_size * input[0].shape[1] * output.shape[1]
    
    # Register hooks
    hooks = []
    for module in model.modules():
        if isinstance(module, (nn.Conv2d, nn.Linear)):
            hooks.append(module.register_forward_hook(hook))
    
    # Run forward pass
    device = next(model.parameters()).device
    dummy_input = torch.zeros(input_size).to(device)
    with torch.no_grad():
        model(dummy_input)
    
    # Remove hooks
    for h in hooks:
        h.remove()
    
    return flops

def analyze_models():
    """Analyze and compare all models"""
    img_size = 1024
    use_instance_norm = True
    
    # Create models
    models = {
        'ResUNet': ResUNet(in_channels=3, out_channels=1, use_instance_norm=use_instance_norm),
        'HRNet': HRNetV2(n_class=1, pretrained=False, use_instance_norm=use_instance_norm),
        'PSPNet': PSPNet(n_class=1, backbone='resnet101', pretrained=False, use_instance_norm=use_instance_norm),
        'TransUNet': TransUNet(img_size=img_size, in_channels=3, out_channels=1, use_instance_norm=use_instance_norm)
    }
    
    # Create table
    table = PrettyTable()
    table.field_names = ["Model", "Total Params", "Trainable Params", "Model Size (MB)", "Est. GFLOPs", "Memory (GB)"]
    
    for name, model in models.items():
        # Move to GPU if available
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = model.to(device)
        model.eval()
        
        # Count parameters
        trainable, total = count_parameters(model)
        
        # Model size in MB
        param_size = 0
        buffer_size = 0
        for param in model.parameters():
            param_size += param.nelement() * param.element_size()
        for buffer in model.buffers():
            buffer_size += buffer.nelement() * buffer.element_size()
        model_size_mb = (param_size + buffer_size) / 1024 / 1024
        
        # Estimate FLOPs
        try:
            flops = get_model_flops(model, (1, 3, img_size, img_size))
            gflops = flops / 1e9
        except:
            gflops = "N/A"
        
        # Estimate memory usage
        try:
            dummy_input = torch.zeros(1, 3, img_size, img_size).to(device)
            torch.cuda.reset_peak_memory_stats()
            with torch.no_grad():
                _ = model(dummy_input)
            memory_gb = torch.cuda.max_memory_allocated() / 1024**3
        except:
            memory_gb = "N/A"
        
        # Add to table
        table.add_row([
            name,
            f"{total:,}",
            f"{trainable:,}",
            f"{model_size_mb:.2f}",
            f"{gflops:.2f}" if isinstance(gflops, float) else gflops,
            f"{memory_gb:.2f}" if isinstance(memory_gb, float) else memory_gb
        ])
        
        # Clean up
        del model
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    print("\n" + "="*80)
    print("MODEL CAPACITY COMPARISON")
    print("="*80)
    print(f"Input Size: {img_size}x{img_size}")
    print(f"Instance Normalization: {use_instance_norm}")
    print("="*80)
    print(table)
    
    # Additional analysis
    print("\n" + "="*80)
    print("MODEL CHARACTERISTICS")
    print("="*80)
    
    print("\n1. ResUNet:")
    print("   - Enhanced U-Net with residual blocks and attention gates")
    print("   - SE blocks for channel attention")
    print("   - Features: [64, 128, 256, 512, 1024]")
    print("   - Best for: High-resolution detail preservation")
    
    print("\n2. HRNet:")
    print("   - Maintains high-resolution representations throughout")
    print("   - 4 parallel streams with different resolutions")
    print("   - Multi-scale fusion at each stage")
    print("   - Best for: Boundary-sensitive segmentation")
    
    print("\n3. PSPNet:")
    print("   - Pyramid pooling module for multi-scale context")
    print("   - ResNet101 backbone")
    print("   - Global context aggregation")
    print("   - Best for: Objects with varying sizes")
    
    print("\n4. TransUNet:")
    print("   - Hybrid CNN-Transformer architecture")
    print("   - Vision Transformer for global context")
    print("   - CNN encoder/decoder for local features")
    print("   - Best for: Complex patterns requiring global understanding")
    
    print("\n" + "="*80)
    print("RECOMMENDATIONS FOR SPHEROID SEGMENTATION")
    print("="*80)
    print("1. Start with HRNet - excellent for microscopy due to high-resolution maintenance")
    print("2. Try TransUNet - can capture complex spheroid patterns with global context")
    print("3. Use ResUNet as baseline - proven architecture for medical imaging")
    print("4. PSPNet for datasets with varying spheroid sizes")
    print("\nAll models have comparable capacity for fair comparison.")

if __name__ == "__main__":
    analyze_models()