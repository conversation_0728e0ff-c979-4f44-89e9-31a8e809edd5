# compare_attention_mechanisms.py - Compare different attention mechanisms
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

import torch
import torch.nn as nn
import time
from models.resunet import ResUNet as OriginalResUNet
from models.resunet_enhanced import EnhancedResUNet
from prettytable import PrettyTable
import numpy as np

def count_parameters(model):
    """Count trainable and total parameters"""
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    return trainable_params, total_params

def measure_inference_time(model, input_size=(1, 3, 1024, 1024), num_runs=10):
    """Measure average inference time"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    model.eval()
    
    dummy_input = torch.randn(input_size).to(device)
    
    # Warm up
    for _ in range(3):
        with torch.no_grad():
            _ = model(dummy_input)
    
    # Measure
    if torch.cuda.is_available():
        torch.cuda.synchronize()
    
    start_time = time.time()
    for _ in range(num_runs):
        with torch.no_grad():
            _ = model(dummy_input)
    
    if torch.cuda.is_available():
        torch.cuda.synchronize()
    
    end_time = time.time()
    avg_time = (end_time - start_time) / num_runs * 1000  # ms
    
    return avg_time

def analyze_attention_mechanisms():
    """Compare different attention mechanisms"""
    img_size = 1024
    use_instance_norm = True
    
    print("\n" + "="*80)
    print("ATTENTION MECHANISM COMPARISON")
    print("="*80)
    
    # Create models with different attention types
    models = {
        'Original ResUNet (SE)': OriginalResUNet(in_channels=3, out_channels=1, 
                                                 features=[64, 128, 256, 512, 1024],
                                                 use_instance_norm=use_instance_norm),
        'Enhanced ResUNet (CBAM)': EnhancedResUNet(in_channels=3, out_channels=1, 
                                                   features=[48, 96, 192, 384],
                                                   attention_type='cbam',
                                                   use_instance_norm=use_instance_norm),
        'Enhanced ResUNet (ECA)': EnhancedResUNet(in_channels=3, out_channels=1, 
                                                  features=[48, 96, 192, 384],
                                                  attention_type='eca',
                                                  use_instance_norm=use_instance_norm),
        'Enhanced ResUNet (Coord)': EnhancedResUNet(in_channels=3, out_channels=1, 
                                                    features=[48, 96, 192, 384],
                                                    attention_type='coord',
                                                    use_instance_norm=use_instance_norm),
        'Enhanced ResUNet (Hybrid)': EnhancedResUNet(in_channels=3, out_channels=1, 
                                                     features=[48, 96, 192, 384],
                                                     attention_type='hybrid',
                                                     use_instance_norm=use_instance_norm),
    }
    
    # Create comparison table
    table = PrettyTable()
    table.field_names = ["Model", "Attention Type", "Total Params", "Model Size (MB)", 
                        "Inference Time (ms)", "Relative Speed"]
    
    # Reference time for speed comparison
    ref_time = None
    
    for name, model in models.items():
        # Count parameters
        trainable, total = count_parameters(model)
        
        # Model size
        param_size = 0
        for param in model.parameters():
            param_size += param.nelement() * param.element_size()
        model_size_mb = param_size / 1024 / 1024
        
        # Inference time
        try:
            inference_time = measure_inference_time(model, (1, 3, img_size, img_size))
            if ref_time is None:
                ref_time = inference_time
            relative_speed = f"{ref_time / inference_time:.2f}x"
        except Exception as e:
            inference_time = "N/A"
            relative_speed = "N/A"
        
        # Extract attention type
        if "Original" in name:
            attention_type = "SE (Squeeze-Excitation)"
        elif "CBAM" in name:
            attention_type = "CBAM"
        elif "ECA" in name:
            attention_type = "ECA-Net"
        elif "Coord" in name:
            attention_type = "Coordinate Attention"
        elif "Hybrid" in name:
            attention_type = "Hybrid (CBAM + Coord)"
        
        # Add to table
        table.add_row([
            name,
            attention_type,
            f"{total:,}",
            f"{model_size_mb:.2f}",
            f"{inference_time:.2f}" if isinstance(inference_time, float) else inference_time,
            relative_speed
        ])
    
    print(table)
    
    # Attention mechanism details
    print("\n" + "="*80)
    print("ATTENTION MECHANISM DETAILS")
    print("="*80)
    
    print("\n1. SE (Squeeze-Excitation) - Original ResUNet:")
    print("   - Channel-wise attention only")
    print("   - Global average pooling + FC layers")
    print("   - Lightweight but limited to channel attention")
    
    print("\n2. CBAM (Convolutional Block Attention Module):")
    print("   - Sequential channel and spatial attention")
    print("   - Both average and max pooling for richer features")
    print("   - More comprehensive attention mechanism")
    
    print("\n3. ECA-Net (Efficient Channel Attention):")
    print("   - Improved channel attention without dimensionality reduction")
    print("   - Uses 1D convolution for cross-channel interaction")
    print("   - More efficient than SE with better performance")
    
    print("\n4. Coordinate Attention:")
    print("   - Encodes position information in attention")
    print("   - Captures long-range dependencies with precise location")
    print("   - Particularly good for position-sensitive tasks")
    
    print("\n5. Hybrid Attention:")
    print("   - Combines multiple attention mechanisms")
    print("   - Channel + Spatial + Coordinate attention")
    print("   - Most comprehensive but computationally heavier")
    
    print("\n" + "="*80)
    print("RECOMMENDATIONS FOR SPHEROID SEGMENTATION")
    print("="*80)
    print("1. CBAM: Best balance of performance and efficiency")
    print("2. ECA-Net: For fastest inference with good channel attention")
    print("3. Coordinate Attention: When precise boundary localization is critical")
    print("4. Hybrid: For maximum accuracy when computation is not a concern")
    
    print("\n" + "="*80)
    print("ARCHITECTURAL IMPROVEMENTS IN ENHANCED RESUNET")
    print("="*80)
    print("1. Modern attention mechanisms (CBAM/ECA/Coord) replace simple SE blocks")
    print("2. Enhanced attention gates with refinement layers")
    print("3. Multi-scale attention fusion in bottleneck")
    print("4. Reduced channel dimensions for similar capacity to other models")
    print("5. Optional hybrid attention combining multiple mechanisms")
    print("6. Better gradient flow with improved residual connections")

if __name__ == "__main__":
    analyze_attention_mechanisms()