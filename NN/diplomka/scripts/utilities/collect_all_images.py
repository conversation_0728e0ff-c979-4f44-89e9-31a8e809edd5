#!/usr/bin/env python3
"""
Collect all images and masks from Deep-Tumour-Spheroid, slimia_final, and SpheroidJ
into two simple folders: images/ and masks/
"""

import os
import shutil
from pathlib import Path
from collections import defaultdict
from tqdm import tqdm

def count_valid_pairs(images_dir, masks_dir):
    """Count valid image-mask pairs in directories"""
    if not images_dir.exists() or not masks_dir.exists():
        return []
    
    valid_pairs = []
    image_extensions = ['.png', '.jpg', '.jpeg', '.tif', '.tiff', '.bmp']
    mask_extensions = ['.png', '.jpg', '.jpeg', '.tif', '.tiff', '.bmp']
    
    # Get all image files
    image_files = []
    for ext in image_extensions:
        image_files.extend(images_dir.glob(f'*{ext}'))
        image_files.extend(images_dir.glob(f'*{ext.upper()}'))
    
    # Get all mask files and create a dict by stem and by name without extension
    mask_dict = {}
    for ext in mask_extensions:
        for mask_path in masks_dir.glob(f'*{ext}'):
            if not mask_path.name.startswith('.'):
                mask_dict[mask_path.stem] = mask_path
                # Also store by name without extension for .ome.tiff matching
                mask_dict[mask_path.name.replace(ext, '')] = mask_path
        for mask_path in masks_dir.glob(f'*{ext.upper()}'):
            if not mask_path.name.startswith('.'):
                mask_dict[mask_path.stem] = mask_path
                # Also store by name without extension for .ome.tiff matching
                mask_dict[mask_path.name.replace(ext.upper(), '')] = mask_path
    
    # Match images with masks by stem (handling .ome.tiff -> .tiff conversion)
    for img_path in image_files:
        if img_path.name.startswith('.'):
            continue
            
        # Try exact stem match first
        if img_path.stem in mask_dict:
            valid_pairs.append((img_path, mask_dict[img_path.stem]))
        # If image is .ome.tiff, try without .ome part
        elif img_path.name.endswith('.ome.tiff') or img_path.name.endswith('.ome.tif'):
            # Remove .ome from stem
            base_stem = img_path.name.replace('.ome.tiff', '').replace('.ome.tif', '').replace('.ome.TIFF', '').replace('.ome.TIF', '')
            if base_stem in mask_dict:
                valid_pairs.append((img_path, mask_dict[base_stem]))
    
    return valid_pairs

def collect_all_data(base_path, output_path):
    """Collect all images and masks from specified datasets"""
    base_path = Path(base_path)
    output_path = Path(output_path)
    
    # Target datasets
    datasets = ['Deep-Tumour-Spheroid', 'slimia_final', 'SpheroidJ']
    
    # Create output directories
    images_dir = output_path / 'images'
    masks_dir = output_path / 'masks'
    images_dir.mkdir(parents=True, exist_ok=True)
    masks_dir.mkdir(parents=True, exist_ok=True)
    
    # Track used names to avoid collisions
    used_names = set()
    total_copied = 0
    
    for dataset in datasets:
        dataset_path = base_path / dataset
        if not dataset_path.exists():
            print(f"Warning: {dataset} not found, skipping")
            continue
        
        print(f"\nProcessing dataset: {dataset}")
        
        # Find all projects in this dataset
        projects = []
        for proj_dir in dataset_path.iterdir():
            if not proj_dir.is_dir() or proj_dir.name.startswith('.'):
                continue
                
            proj_images_dir = proj_dir / 'images'
            proj_masks_dir = proj_dir / 'masks'
            
            if proj_images_dir.exists() and proj_masks_dir.exists():
                valid_pairs = count_valid_pairs(proj_images_dir, proj_masks_dir)
                if valid_pairs:
                    projects.append((proj_dir.name, valid_pairs))
        
        print(f"Found {len(projects)} projects in {dataset}")
        
        # Copy all files from this dataset
        for project_name, valid_pairs in projects:
            print(f"  Copying {project_name}: {len(valid_pairs)} image-mask pairs")
            
            for img_path, mask_path in tqdm(valid_pairs, desc=f"  {project_name}"):
                # Create unique filename with dataset and project prefix
                base_name = img_path.stem
                img_ext = img_path.suffix
                mask_ext = mask_path.suffix
                new_base_name = f"{dataset}_{project_name}_{base_name}"
                
                # Handle name collisions
                counter = 1
                while new_base_name in used_names:
                    new_base_name = f"{dataset}_{project_name}_{base_name}_{counter}"
                    counter += 1
                
                used_names.add(new_base_name)
                
                # Copy files with their original extensions
                dst_img = images_dir / f"{new_base_name}{img_ext}"
                dst_mask = masks_dir / f"{new_base_name}{mask_ext}"
                
                shutil.copy2(img_path, dst_img)
                shutil.copy2(mask_path, dst_mask)
                
                total_copied += 1
    
    return total_copied

def main():
    base_path = '/Volumes/T7/Datasets/sféroidy/all'
    output_path = '/Volumes/T7/Datasets/sféroidy/final'
    
    print("Collecting all images and masks...")
    print(f"Source: {base_path}")
    print(f"Target: {output_path}")
    print(f"Datasets: Deep-Tumour-Spheroid, slimia_final, SpheroidJ")
    
    total_copied = collect_all_data(base_path, output_path)
    
    # Final count
    images_count = len(list((Path(output_path) / 'images').glob('*')))
    masks_count = len(list((Path(output_path) / 'masks').glob('*')))
    
    print("\n" + "="*60)
    print("COLLECTION COMPLETE")
    print("="*60)
    print(f"Output directory: {output_path}")
    print(f"Images copied: {images_count}")
    print(f"Masks copied: {masks_count}")
    print(f"Total pairs: {min(images_count, masks_count)}")

if __name__ == '__main__':
    main()