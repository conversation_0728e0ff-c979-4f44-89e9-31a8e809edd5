#!/usr/bin/env python3
"""
Create train/val/test split from multiple spheroid datasets.
Splits are done by cell lines (projects), not individual images.
Val/test sets only from all_spheroseg dataset.
"""

import os
import shutil
from pathlib import Path
import random
import json
from collections import defaultdict
from tqdm import tqdm

def count_valid_pairs(images_dir, masks_dir):
    """Count valid image-mask pairs in directories"""
    if not images_dir.exists() or not masks_dir.exists():
        return []
    
    valid_pairs = []
    image_extensions = ['.png', '.jpg', '.jpeg', '.tif', '.tiff', '.bmp']
    mask_extensions = ['.png', '.jpg', '.jpeg', '.tif', '.tiff', '.bmp']
    
    # Get all image files
    image_files = []
    for ext in image_extensions:
        image_files.extend(images_dir.glob(f'*{ext}'))
        image_files.extend(images_dir.glob(f'*{ext.upper()}'))
    
    # Get all mask files and create a dict by stem
    mask_dict = {}
    for ext in mask_extensions:
        for mask_path in masks_dir.glob(f'*{ext}'):
            if not mask_path.name.startswith('.'):
                mask_dict[mask_path.stem] = mask_path
        for mask_path in masks_dir.glob(f'*{ext.upper()}'):
            if not mask_path.name.startswith('.'):
                mask_dict[mask_path.stem] = mask_path
    
    # Match images with masks by stem
    for img_path in image_files:
        if img_path.name.startswith('.'):
            continue
            
        # Check for corresponding mask by stem
        if img_path.stem in mask_dict:
            valid_pairs.append((img_path, mask_dict[img_path.stem]))
    
    return valid_pairs

def analyze_datasets(base_path):
    """Analyze all datasets and return project information"""
    base_path = Path(base_path)
    datasets = ['all_spheroseg', 'Deep-Tumour-Spheroid', 'slimia_final', 'SpheroidJ']
    
    project_info = {}
    
    for dataset in datasets:
        dataset_path = base_path / dataset
        if not dataset_path.exists():
            print(f"Warning: {dataset} not found")
            continue
            
        project_info[dataset] = {}
        
        # Find all projects (subdirectories with images/masks)
        for proj_dir in dataset_path.iterdir():
            if not proj_dir.is_dir() or proj_dir.name.startswith('.'):
                continue
                
            images_dir = proj_dir / 'images'
            masks_dir = proj_dir / 'masks'
            
            if images_dir.exists() and masks_dir.exists():
                valid_pairs = count_valid_pairs(images_dir, masks_dir)
                if valid_pairs:
                    project_info[dataset][proj_dir.name] = {
                        'path': proj_dir,
                        'valid_pairs': valid_pairs,
                        'count': len(valid_pairs)
                    }
    
    return project_info

def create_splits(project_info, output_path, train_ratio=0.8, val_ratio=0.1, test_ratio=0.1):
    """Create train/val/test splits maintaining ratios"""
    output_path = Path(output_path)
    
    # Get all_spheroseg projects for val/test
    spheroseg_projects = list(project_info.get('all_spheroseg', {}).keys())
    
    if len(spheroseg_projects) < 2:
        raise ValueError("Need at least 2 projects in all_spheroseg for val/test splits")
    
    # Shuffle and split spheroseg projects
    random.shuffle(spheroseg_projects)
    n_val_test = max(1, len(spheroseg_projects) // 2)
    val_projects = spheroseg_projects[:n_val_test]
    test_projects = spheroseg_projects[n_val_test:2*n_val_test]
    
    # All other projects go to train
    train_projects = []
    for dataset, projects in project_info.items():
        for project in projects:
            if dataset == 'all_spheroseg' and (project in val_projects or project in test_projects):
                continue
            train_projects.append((dataset, project))
    
    # Count total images in each split
    train_count = sum(project_info[d][p]['count'] for d, p in train_projects)
    val_count = sum(project_info['all_spheroseg'][p]['count'] for p in val_projects)
    test_count = sum(project_info['all_spheroseg'][p]['count'] for p in test_projects)
    total_count = train_count + val_count + test_count
    
    print(f"\nInitial split:")
    print(f"Train: {train_count} images ({train_count/total_count*100:.1f}%)")
    print(f"Val: {val_count} images ({val_count/total_count*100:.1f}%)")
    print(f"Test: {test_count} images ({test_count/total_count*100:.1f}%)")
    
    # Adjust if needed to get closer to 80/10/10
    target_val = int(total_count * val_ratio)
    target_test = int(total_count * test_ratio)
    
    # Try to balance val/test by moving projects
    remaining_spheroseg = [p for p in spheroseg_projects if p not in val_projects and p not in test_projects]
    
    while val_count < target_val and remaining_spheroseg:
        # Add project to val
        proj = remaining_spheroseg.pop(0)
        val_projects.append(proj)
        val_count += project_info['all_spheroseg'][proj]['count']
        train_count -= project_info['all_spheroseg'][proj]['count']
        train_projects = [(d, p) for d, p in train_projects if not (d == 'all_spheroseg' and p == proj)]
    
    while test_count < target_test and remaining_spheroseg:
        # Add project to test
        proj = remaining_spheroseg.pop(0)
        test_projects.append(proj)
        test_count += project_info['all_spheroseg'][proj]['count']
        train_count -= project_info['all_spheroseg'][proj]['count']
        train_projects = [(d, p) for d, p in train_projects if not (d == 'all_spheroseg' and p == proj)]
    
    print(f"\nFinal split:")
    print(f"Train: {train_count} images ({train_count/total_count*100:.1f}%) - {len(train_projects)} projects")
    print(f"Val: {val_count} images ({val_count/total_count*100:.1f}%) - {len(val_projects)} projects")
    print(f"Test: {test_count} images ({test_count/total_count*100:.1f}%) - {len(test_projects)} projects")
    
    return {
        'train': train_projects,
        'val': [('all_spheroseg', p) for p in val_projects],
        'test': [('all_spheroseg', p) for p in test_projects]
    }

def copy_split_data(project_info, splits, output_path):
    """Copy data according to splits"""
    output_path = Path(output_path)
    
    # Create output directories
    for split in ['train', 'val', 'test']:
        (output_path / split / 'images').mkdir(parents=True, exist_ok=True)
        (output_path / split / 'masks').mkdir(parents=True, exist_ok=True)
    
    # Track filenames to avoid collisions
    used_names = defaultdict(set)
    
    # Copy files
    for split_name, projects in splits.items():
        print(f"\nCopying {split_name} data...")
        
        for dataset, project in tqdm(projects, desc=f"Projects in {split_name}"):
            proj_data = project_info[dataset][project]
            
            for img_path, mask_path in proj_data['valid_pairs']:
                # Create unique filename with dataset and project prefix
                base_name = img_path.stem
                img_ext = img_path.suffix
                mask_ext = mask_path.suffix
                new_base_name = f"{dataset}_{project}_{base_name}"
                
                # Handle name collisions
                counter = 1
                while new_base_name in used_names[split_name]:
                    new_base_name = f"{dataset}_{project}_{base_name}_{counter}"
                    counter += 1
                
                used_names[split_name].add(new_base_name)
                
                # Copy files with their original extensions
                dst_img = output_path / split_name / 'images' / f"{new_base_name}{img_ext}"
                dst_mask = output_path / split_name / 'masks' / f"{new_base_name}{mask_ext}"
                
                shutil.copy2(img_path, dst_img)
                shutil.copy2(mask_path, dst_mask)
    
    # Save split information
    split_info = {
        'splits': {k: [(d, p) for d, p in v] for k, v in splits.items()},
        'counts': {
            split: len(list((output_path / split / 'images').glob('*')))
            for split in ['train', 'val', 'test']
        }
    }
    
    with open(output_path / 'split_info.json', 'w') as f:
        json.dump(split_info, f, indent=2)
    
    return split_info

def main():
    base_path = '/Volumes/T7/Datasets/sféroidy/all'
    output_path = '/Volumes/T7/Datasets/sféroidy/final'
    
    print("Analyzing datasets...")
    project_info = analyze_datasets(base_path)
    
    # Print dataset summary
    print("\nDataset Summary:")
    print("="*60)
    for dataset, projects in project_info.items():
        total_images = sum(p['count'] for p in projects.values())
        print(f"{dataset}: {len(projects)} projects, {total_images} valid image-mask pairs")
        for proj, info in projects.items():
            print(f"  - {proj}: {info['count']} images")
    
    # Create splits
    print("\nCreating splits...")
    splits = create_splits(project_info, output_path)
    
    # Copy data
    print("\nCopying data to final directory...")
    split_info = copy_split_data(project_info, splits, output_path)
    
    # Final report
    print("\n" + "="*60)
    print("DATASET SPLIT COMPLETE")
    print("="*60)
    print(f"Output directory: {output_path}")
    print(f"Train: {split_info['counts']['train']} images")
    print(f"Val: {split_info['counts']['val']} images")
    print(f"Test: {split_info['counts']['test']} images")
    print(f"Total: {sum(split_info['counts'].values())} images")
    print("\nSplit information saved to split_info.json")

if __name__ == '__main__':
    random.seed(42)  # For reproducibility
    main()