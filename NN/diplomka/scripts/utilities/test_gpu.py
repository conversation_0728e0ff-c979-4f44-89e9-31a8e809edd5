#!/usr/bin/env python3
"""
Test GPU availability and memory for training
"""

import torch
import torch.nn as nn
import numpy as np
from pathlib import Path

def test_gpu_setup():
    """Test GPU availability and basic operations"""
    print("=" * 60)
    print("GPU Setup Test")
    print("=" * 60)
    
    # Check CUDA availability
    print(f"CUDA available: {torch.cuda.is_available()}")
    print(f"CUDA version: {torch.version.cuda}")
    print(f"PyTorch version: {torch.__version__}")
    print(f"Number of GPUs: {torch.cuda.device_count()}")
    print()
    
    # Check each GPU
    for i in range(torch.cuda.device_count()):
        props = torch.cuda.get_device_properties(i)
        print(f"GPU {i}: {props.name}")
        print(f"  Total memory: {props.total_memory / 1024**3:.2f} GB")
        print(f"  Memory allocated: {torch.cuda.memory_allocated(i) / 1024**3:.2f} GB")
        print(f"  Memory cached: {torch.cuda.memory_reserved(i) / 1024**3:.2f} GB")
        print(f"  Free memory: {(props.total_memory - torch.cuda.memory_allocated(i)) / 1024**3:.2f} GB")
        print()
    
    # Test basic tensor operations
    print("Testing basic operations...")
    try:
        # Test on each GPU
        for i in range(torch.cuda.device_count()):
            device = torch.device(f'cuda:{i}')
            x = torch.randn(1024, 1024, device=device)
            y = torch.randn(1024, 1024, device=device)
            z = torch.matmul(x, y)
            print(f"GPU {i}: Basic operations OK")
            del x, y, z
            torch.cuda.empty_cache()
    except Exception as e:
        print(f"Error in basic operations: {e}")
    print()
    
    # Test memory allocation
    print("Testing memory allocation (1024x1024 images)...")
    for i in range(torch.cuda.device_count()):
        device = torch.device(f'cuda:{i}')
        max_batch = 1
        
        # Binary search for max batch size
        low, high = 1, 64
        while low < high:
            mid = (low + high + 1) // 2
            try:
                # Simulate batch of images
                batch = torch.randn(mid, 3, 1024, 1024, device=device)
                del batch
                torch.cuda.empty_cache()
                low = mid
            except RuntimeError as e:
                if "out of memory" in str(e):
                    high = mid - 1
                else:
                    raise e
        
        max_batch = low
        print(f"GPU {i}: Max batch size for 1024x1024 images: {max_batch}")
    print()
    
    # Test multi-GPU communication
    if torch.cuda.device_count() > 1:
        print("Testing multi-GPU communication...")
        try:
            x = torch.randn(1000, 1000, device='cuda:0')
            y = x.to('cuda:1')
            z = y.to('cuda:0')
            assert torch.allclose(x, z)
            print("Multi-GPU communication: OK")
            del x, y, z
            torch.cuda.empty_cache()
        except Exception as e:
            print(f"Multi-GPU communication error: {e}")
    
    print("\n" + "=" * 60)
    print("GPU test complete!")
    print("=" * 60)

if __name__ == "__main__":
    test_gpu_setup()