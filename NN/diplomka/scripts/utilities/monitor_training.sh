#!/bin/bash
# Monitor training progress and GPU usage

echo "Monitoring training..."
echo "Press Ctrl+C to stop"
echo ""

# Function to get GPU stats
get_gpu_stats() {
    nvidia-smi --query-gpu=index,name,memory.used,memory.total,utilization.gpu,temperature.gpu \
                --format=csv,noheader,nounits | \
    awk -F', ' '{printf "GPU %s: %s | Mem: %s/%s MB (%.1f%%) | Util: %s%% | Temp: %s°C\n", 
                 $1, $2, $3, $4, ($3/$4)*100, $5, $6}'
}

# Monitor loop
while true; do
    clear
    echo "=== GPU Status ==="
    get_gpu_stats
    echo ""
    
    # Check if training is running
    if pgrep -f "CNN_main_spheroid.py" > /dev/null; then
        echo "=== Training Status ==="
        echo "Training is RUNNING"
        
        # Try to get latest log info
        LATEST_LOG=$(ls -t outputs/*/train.log 2>/dev/null | head -1)
        if [ -f "$LATEST_LOG" ]; then
            echo ""
            echo "=== Latest Training Info ==="
            tail -5 "$LATEST_LOG" | grep -E "(Epoch|Loss:|IoU:|Best)"
        fi
    else
        echo "=== Training Status ==="
        echo "No training process found"
    fi
    
    echo ""
    echo "Refreshing in 2 seconds..."
    sleep 2
done