#!/usr/bin/env python3
"""
Kompletní evaluační skript pro všechny natrénované modely
Vyhodnocuje metriky segmentace a časovou náročnost
"""

import os
import sys
import time
import argparse
import json
from pathlib import Path
import numpy as np
import torch
import torch.nn.functional as F
from torch.utils.data import DataLoader
from PIL import Image
import cv2
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd

# Import model architektur
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from models.resunet import ResUNet
from models.hrnet import HRNetV2
from models.pspnet import PSPNet
from models.transunet import TransUNet

class SegmentationDataset(torch.utils.data.Dataset):
    """Dataset pro načítání obr<PERSON>zk<PERSON> a masek"""
    
    def __init__(self, images_dir, masks_dir, transform=None):
        self.images_dir = Path(images_dir)
        self.masks_dir = Path(masks_dir)
        self.transform = transform
        
        # Najdi všechny obr<PERSON><PERSON><PERSON> (vyfiltruj skryté soubory)
        image_extensions = ['.png', '.jpg', '.jpeg', '.tif', '.tiff', '.bmp']
        self.image_files = []
        
        for ext in image_extensions:
            files = list(self.images_dir.glob(f'*{ext}')) + list(self.images_dir.glob(f'*{ext.upper()}'))
            # Filtruj skryté soubory (začínající tečkou nebo ._)
            files = [f for f in files if not f.name.startswith('.') and not f.name.startswith('._')]
            self.image_files.extend(files)
        
        # Najdi odpovídající masky
        self.valid_pairs = []
        for img_file in self.image_files:
            mask_file = self.find_mask_file(img_file)
            if mask_file and mask_file.exists():
                self.valid_pairs.append((img_file, mask_file))
        
        print(f"Nalezeno {len(self.valid_pairs)} platných párů obrázek-maska")
    
    def find_mask_file(self, img_file):
        """Najdi odpovídající soubor masky"""
        base_name = img_file.stem
        mask_extensions = ['.png', '.jpg', '.jpeg', '.tif', '.tiff', '.bmp']
        
        for ext in mask_extensions:
            mask_file = self.masks_dir / f"{base_name}{ext}"
            if mask_file.exists():
                return mask_file
            mask_file = self.masks_dir / f"{base_name}{ext.upper()}"
            if mask_file.exists():
                return mask_file
        return None
    
    def __len__(self):
        return len(self.valid_pairs)
    
    def __getitem__(self, idx):
        img_path, mask_path = self.valid_pairs[idx]
        
        # Načti obrázek
        image = Image.open(img_path).convert('RGB')
        image = np.array(image)
        
        # Načti masku
        mask = Image.open(mask_path).convert('L')
        mask = np.array(mask)
        
        # Normalizuj masku na 0-1
        mask = (mask > 128).astype(np.float32)
        
        # Resize na 1024x1024
        image = cv2.resize(image, (1024, 1024))
        mask = cv2.resize(mask, (1024, 1024))
        
        # Převeď na tensor
        image = torch.from_numpy(image).permute(2, 0, 1).float() / 255.0
        mask = torch.from_numpy(mask).float()
        
        return image, mask, str(img_path.name)

def load_model(model_name, model_path, device):
    """Načti model podle typu"""
    if model_name == 'resunet':
        model = ResUNet(in_channels=3, out_channels=1, use_instance_norm=True)
    elif model_name == 'hrnet':
        model = HRNetV2(n_class=1, use_instance_norm=True)
    elif model_name == 'pspnet':
        # Zkusíme načíst PSPNet s různými konfiguracemi
        try:
            model = PSPNet(n_class=1, backbone='resnet50', use_instance_norm=True)
        except:
            model = PSPNet(n_class=1, backbone='resnet101', use_instance_norm=True)
    elif model_name == 'transunet':
        model = TransUNet(in_channels=3, out_channels=1, img_size=1024, use_instance_norm=True)
    else:
        raise ValueError(f"Neznámý model: {model_name}")
    
    # Načti váhy
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
    else:
        model.load_state_dict(checkpoint)
    
    model.to(device)
    model.eval()
    return model

def calculate_metrics(pred_mask, true_mask, threshold=0.5):
    """Vypočítej segmentační metriky"""
    pred_binary = (pred_mask > threshold).astype(np.uint8).flatten()
    true_binary = true_mask.astype(np.uint8).flatten()
    
    # Základní metriky
    accuracy = accuracy_score(true_binary, pred_binary)
    precision = precision_score(true_binary, pred_binary, zero_division=0)
    recall = recall_score(true_binary, pred_binary, zero_division=0)
    f1 = f1_score(true_binary, pred_binary, zero_division=0)
    
    # IoU (Intersection over Union)
    intersection = np.logical_and(pred_binary, true_binary).sum()
    union = np.logical_or(pred_binary, true_binary).sum()
    iou = intersection / union if union > 0 else 0
    
    # Dice coefficient
    dice = 2 * intersection / (pred_binary.sum() + true_binary.sum()) if (pred_binary.sum() + true_binary.sum()) > 0 else 0
    
    # Specificity (True Negative Rate)
    tn = np.logical_and(pred_binary == 0, true_binary == 0).sum()
    fp = np.logical_and(pred_binary == 1, true_binary == 0).sum()
    specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
    
    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1,
        'iou': iou,
        'dice': dice,
        'specificity': specificity
    }

def evaluate_model(model, dataloader, device, model_name):
    """Evaluuj model na celém datasetu"""
    all_metrics = []
    inference_times = []
    
    print(f"Evaluuji model {model_name}...")
    
    with torch.no_grad():
        for batch_idx, (images, masks, filenames) in enumerate(dataloader):
            images = images.to(device)
            masks = masks.to(device)
            
            # Měř čas inference
            start_time = time.time()
            outputs = model(images)
            if isinstance(outputs, tuple):  # TransUNet returns tuple
                outputs = outputs[0]
            inference_time = time.time() - start_time
            
            # Aplikuj sigmoid
            outputs = torch.sigmoid(outputs)
            
            # Převeď na numpy
            pred_masks = outputs.cpu().numpy()
            true_masks = masks.cpu().numpy()
            
            # Vypočítej metriky pro každý obrázek v batchi
            for i in range(pred_masks.shape[0]):
                metrics = calculate_metrics(pred_masks[i, 0], true_masks[i])
                metrics['filename'] = filenames[i]
                metrics['inference_time'] = inference_time / pred_masks.shape[0]  # čas na obrázek
                all_metrics.append(metrics)
                inference_times.append(metrics['inference_time'])
            
            if (batch_idx + 1) % 10 == 0:
                print(f"  Zpracováno {batch_idx + 1}/{len(dataloader)} batchů")
    
    return all_metrics, inference_times

def find_optimal_threshold(model, dataloader, device):
    """Najdi optimální threshold pro model"""
    print("Hledám optimální threshold...")
    
    thresholds = np.arange(0.1, 0.9, 0.05)
    threshold_metrics = []
    
    with torch.no_grad():
        all_predictions = []
        all_targets = []
        
        for images, masks, _ in dataloader:
            images = images.to(device)
            outputs = model(images)
            if isinstance(outputs, tuple):
                outputs = outputs[0]
            outputs = torch.sigmoid(outputs)
            
            all_predictions.append(outputs.cpu().numpy())
            all_targets.append(masks.numpy())
        
        all_predictions = np.concatenate(all_predictions, axis=0)
        all_targets = np.concatenate(all_targets, axis=0)
        
        for threshold in thresholds:
            metrics_list = []
            for i in range(all_predictions.shape[0]):
                metrics = calculate_metrics(all_predictions[i, 0], all_targets[i], threshold)
                metrics_list.append(metrics)
            
            avg_f1 = np.mean([m['f1_score'] for m in metrics_list])
            avg_iou = np.mean([m['iou'] for m in metrics_list])
            threshold_metrics.append({
                'threshold': threshold,
                'f1_score': avg_f1,
                'iou': avg_iou
            })
    
    # Najdi threshold s nejvyšším F1 score
    best_threshold = max(threshold_metrics, key=lambda x: x['f1_score'])
    return best_threshold['threshold'], threshold_metrics

def save_results(results, output_dir):
    """Ulož výsledky do souborů"""
    output_dir = Path(output_dir)
    output_dir.mkdir(exist_ok=True)
    
    # Ulož detailed results jako JSON
    with open(output_dir / 'detailed_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    # Vytvoř summary tabulku
    summary_data = []
    for model_name, data in results.items():
        if model_name == 'dataset_info':
            continue
            
        metrics = data['metrics']
        avg_metrics = {
            'Model': model_name,
            'Accuracy': np.mean([m['accuracy'] for m in metrics]),
            'Precision': np.mean([m['precision'] for m in metrics]),
            'Recall': np.mean([m['recall'] for m in metrics]),
            'F1-Score': np.mean([m['f1_score'] for m in metrics]),
            'IoU': np.mean([m['iou'] for m in metrics]),
            'Dice': np.mean([m['dice'] for m in metrics]),
            'Specificity': np.mean([m['specificity'] for m in metrics]),
            'Avg_Inference_Time': np.mean(data['inference_times']),
            'Std_Inference_Time': np.std(data['inference_times']),
            'Optimal_Threshold': data['optimal_threshold']
        }
        summary_data.append(avg_metrics)
    
    # Ulož jako CSV
    df = pd.DataFrame(summary_data)
    df.to_csv(output_dir / 'model_comparison.csv', index=False)
    
    # Vytvoř grafy pouze pokud máme data
    if not df.empty:
        create_visualizations(df, output_dir)
    else:
        print("Žádné modely nebyly úspěšně evaluovány - přeskakuji vytváření grafů")
    
    print(f"Výsledky uloženy do: {output_dir}")

def create_visualizations(df, output_dir):
    """Vytvoř vizualizace výsledků"""
    plt.style.use('seaborn-v0_8')
    
    # 1. Porovnání hlavních metrik
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    metrics_to_plot = ['F1-Score', 'IoU', 'Dice', 'Accuracy']
    for i, metric in enumerate(metrics_to_plot):
        ax = axes[i//2, i%2]
        bars = ax.bar(df['Model'], df[metric])
        ax.set_title(f'{metric} Comparison')
        ax.set_ylabel(metric)
        ax.tick_params(axis='x', rotation=45)
        
        # Přidej hodnoty na bary
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{height:.3f}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig(output_dir / 'metrics_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. Časová náročnost
    plt.figure(figsize=(10, 6))
    bars = plt.bar(df['Model'], df['Avg_Inference_Time'] * 1000)  # převeď na ms
    plt.title('Average Inference Time Comparison')
    plt.ylabel('Inference Time (ms)')
    plt.xticks(rotation=45)
    
    # Přidej error bars
    plt.errorbar(df['Model'], df['Avg_Inference_Time'] * 1000, 
                yerr=df['Std_Inference_Time'] * 1000, 
                fmt='none', color='black', capsize=5)
    
    # Přidej hodnoty na bary
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height,
                f'{height:.1f}ms', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig(output_dir / 'inference_time_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. Radar chart pro všechny metriky
    create_radar_chart(df, output_dir)

def create_radar_chart(df, output_dir):
    """Vytvoř radar chart pro porovnání modelů"""
    from math import pi
    
    # Metriky pro radar chart
    metrics = ['Accuracy', 'Precision', 'Recall', 'F1-Score', 'IoU', 'Dice', 'Specificity']
    
    # Počet metrik
    N = len(metrics)
    
    # Úhly pro každou metriku
    angles = [n / float(N) * 2 * pi for n in range(N)]
    angles += angles[:1]  # kompletní kruh
    
    fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
    
    colors = ['red', 'blue', 'green', 'orange', 'purple']
    
    for i, (_, row) in enumerate(df.iterrows()):
        values = [row[metric] for metric in metrics]
        values += values[:1]  # kompletní kruh
        
        ax.plot(angles, values, 'o-', linewidth=2, label=row['Model'], color=colors[i % len(colors)])
        ax.fill(angles, values, alpha=0.25, color=colors[i % len(colors)])
    
    # Přidej labels
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(metrics)
    ax.set_ylim(0, 1)
    ax.set_title('Model Performance Radar Chart', size=16, weight='bold', pad=20)
    ax.legend(loc='upper right', bbox_to_anchor=(1.2, 1.0))
    
    plt.tight_layout()
    plt.savefig(output_dir / 'radar_chart.png', dpi=300, bbox_inches='tight')
    plt.close()

def main():
    parser = argparse.ArgumentParser(description='Evaluace všech natrénovaných modelů')
    parser.add_argument('--dataset_path', required=True, help='Cesta k testovacímu datasetu')
    parser.add_argument('--models_path', required=True, help='Cesta ke složce s natrénovanými modely')
    parser.add_argument('--output_dir', default='evaluation_results', help='Výstupní složka pro výsledky')
    parser.add_argument('--batch_size', type=int, default=1, help='Batch size pro evaluaci')
    parser.add_argument('--find_threshold', action='store_true', help='Najdi optimální threshold pro každý model')
    
    args = parser.parse_args()
    
    # Zkontroluj cesty
    dataset_path = Path(args.dataset_path)
    models_path = Path(args.models_path)
    
    if not dataset_path.exists():
        print(f"Dataset path neexistuje: {dataset_path}")
        return
    
    if not models_path.exists():
        print(f"Models path neexistuje: {models_path}")
        return
    
    images_dir = dataset_path / 'images'
    masks_dir = dataset_path / 'masks'
    
    if not images_dir.exists() or not masks_dir.exists():
        print(f"Složky images nebo masks neexistují v {dataset_path}")
        return
    
    # Nastavení zařízení
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Používám zařízení: {device}")
    
    # Vytvoř dataset
    dataset = SegmentationDataset(images_dir, masks_dir)
    dataloader = DataLoader(dataset, batch_size=args.batch_size, shuffle=False)
    
    # Najdi všechny natrénované modely
    model_configs = [
        ('resunet', 'resunet_finetuned'),
        ('hrnet', 'hrnet_finetuned'),
        ('pspnet', 'pspnet_finetuned')
        # ('transunet', 'transunet_optimized')  # Model neexistuje
    ]
    
    results = {
        'dataset_info': {
            'path': str(dataset_path),
            'num_images': len(dataset),
            'device': str(device)
        }
    }
    
    # Evaluuj každý model
    for model_name, folder_name in model_configs:
        model_folder = models_path / 'trained_models' / folder_name
        model_file = model_folder / 'best_model.pth'
        
        if not model_file.exists():
            print(f"Model {model_name} nenalezen: {model_file}")
            continue
        
        print(f"\n{'='*50}")
        print(f"Evaluuji model: {model_name}")
        print(f"Model cesta: {model_file}")
        
        try:
            # Načti model
            model = load_model(model_name, model_file, device)
            
            # Najdi optimální threshold pokud je požadováno
            optimal_threshold = 0.5
            if args.find_threshold:
                optimal_threshold, _ = find_optimal_threshold(model, dataloader, device)
                print(f"Optimální threshold: {optimal_threshold:.3f}")
            
            # Evaluuj model
            metrics, inference_times = evaluate_model(model, dataloader, device, model_name)
            
            # Uložení výsledků
            results[model_name] = {
                'model_path': str(model_file),
                'optimal_threshold': optimal_threshold,
                'metrics': metrics,
                'inference_times': inference_times,
                'avg_metrics': {
                    'accuracy': np.mean([m['accuracy'] for m in metrics]),
                    'precision': np.mean([m['precision'] for m in metrics]),
                    'recall': np.mean([m['recall'] for m in metrics]),
                    'f1_score': np.mean([m['f1_score'] for m in metrics]),
                    'iou': np.mean([m['iou'] for m in metrics]),
                    'dice': np.mean([m['dice'] for m in metrics]),
                    'specificity': np.mean([m['specificity'] for m in metrics]),
                },
                'time_stats': {
                    'avg_inference_time': np.mean(inference_times),
                    'std_inference_time': np.std(inference_times),
                    'min_inference_time': np.min(inference_times),
                    'max_inference_time': np.max(inference_times)
                }
            }
            
            print(f"Průměrné metriky pro {model_name}:")
            for metric, value in results[model_name]['avg_metrics'].items():
                print(f"  {metric}: {value:.4f}")
            print(f"  Průměrný čas inference: {np.mean(inference_times)*1000:.2f} ms")
            
        except Exception as e:
            print(f"Chyba při evaluaci {model_name}: {e}")
            continue
    
    # Ulož výsledky
    save_results(results, args.output_dir)
    
    print(f"\n{'='*50}")
    print("Evaluace dokončena!")
    print(f"Výsledky uloženy do: {args.output_dir}")

if __name__ == '__main__':
    main()