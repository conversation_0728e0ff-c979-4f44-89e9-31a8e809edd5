#!/usr/bin/env python3
"""
Specialized training script for new PSPNet architecture with auxiliary loss support.
Handles the dual output (main + auxiliary) during training and single output during evaluation.
"""

import os
import sys
import argparse
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP
import numpy as np
from pathlib import Path
import json
from datetime import datetime
import logging

# Add project root to path
sys.path.append('/home/<USER>/SpheroSeg/NN/diplomka')

from models.pspnet_new import PSPNet
from CNN_main_spheroid import CachedSpheroidDataset, CombinedLoss, calculate_metrics, get_training_augmentation, get_validation_augmentation

class PSPNetTrainer:
    """Specialized trainer for PSPNet with auxiliary loss support"""
    
    def __init__(self, args):
        self.args = args
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.setup_logging()
        self.setup_model()
        self.setup_data()
        self.setup_training()
        
    def setup_logging(self):
        """Setup logging configuration"""
        log_dir = Path(self.args.output_dir) / 'logs'
        log_dir.mkdir(parents=True, exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / 'training.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_model(self):
        """Setup PSPNet model with auxiliary loss support"""
        self.logger.info("Setting up PSPNet model...")
        
        self.model = PSPNet(
            n_class=1,
            backbone='resnet101',
            pretrained=True,
            use_instance_norm=self.args.use_instance_norm
        )
        
        # Count parameters
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        
        self.logger.info(f"Model: PSPNet with ResNet101 backbone")
        self.logger.info(f"Total parameters: {total_params:,} ({total_params/1_000_000:.2f}M)")
        self.logger.info(f"Trainable parameters: {trainable_params:,} ({trainable_params/1_000_000:.2f}M)")
        
        # Move to device
        self.model = self.model.to(self.device)
        
        # Setup distributed training if multiple GPUs
        if torch.cuda.device_count() > 1:
            self.logger.info(f"Using {torch.cuda.device_count()} GPUs")
            self.model = nn.DataParallel(self.model)
            
    def setup_data(self):
        """Setup data loaders"""
        self.logger.info("Setting up data loaders...")
        
        # Transforms
        train_transform = get_training_augmentation(self.args.img_size)
        val_transform = get_validation_augmentation(self.args.img_size)

        # Datasets
        train_dataset = CachedSpheroidDataset(
            dataset_dir=self.args.dataset_path,
            split='train',
            transform=train_transform,
            use_cache=True
        )

        val_dataset = CachedSpheroidDataset(
            dataset_dir=self.args.dataset_path,
            split='val',
            transform=val_transform,
            use_cache=True
        )
        
        # Data loaders
        self.train_loader = DataLoader(
            train_dataset,
            batch_size=self.args.batch_size,
            shuffle=True,
            num_workers=self.args.num_workers,
            pin_memory=True,
            drop_last=True
        )
        
        self.val_loader = DataLoader(
            val_dataset,
            batch_size=self.args.batch_size,
            shuffle=False,
            num_workers=self.args.num_workers,
            pin_memory=True
        )
        
        self.logger.info(f"Training samples: {len(train_dataset)}")
        self.logger.info(f"Validation samples: {len(val_dataset)}")
        self.logger.info(f"Training batches: {len(self.train_loader)}")
        self.logger.info(f"Validation batches: {len(self.val_loader)}")
        
    def setup_training(self):
        """Setup optimizer, scheduler, and loss function"""
        self.logger.info("Setting up training components...")
        
        # Optimizer
        if self.args.optimizer == 'adamw':
            self.optimizer = optim.AdamW(
                self.model.parameters(),
                lr=self.args.lr,
                weight_decay=self.args.weight_decay,
                betas=(0.9, 0.999)
            )
        else:
            self.optimizer = optim.Adam(
                self.model.parameters(),
                lr=self.args.lr,
                weight_decay=self.args.weight_decay
            )
            
        # Scheduler
        if self.args.scheduler == 'cosine':
            self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer,
                T_max=self.args.epochs,
                eta_min=self.args.min_lr
            )
        elif self.args.scheduler == 'reduce':
            self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer,
                mode='max',
                factor=0.5,
                patience=self.args.patience // 2
            )
        else:
            self.scheduler = None
            
        # Loss function
        self.criterion = CombinedLoss(
            focal_weight=self.args.focal_weight,
            dice_weight=self.args.dice_weight,
            iou_weight=self.args.iou_weight
        )
        
        # Mixed precision scaler
        self.scaler = torch.amp.GradScaler('cuda') if self.args.mixed_precision else None
        
        # Training state
        self.best_iou = 0.0
        self.best_loss = float('inf')
        self.patience_counter = 0
        
    def train_epoch(self, epoch):
        """Train for one epoch with auxiliary loss"""
        self.model.train()
        running_loss = 0.0
        running_main_loss = 0.0
        running_aux_loss = 0.0
        
        for batch_idx, (images, masks) in enumerate(self.train_loader):
            images = images.to(self.device)
            masks = masks.float().unsqueeze(1).to(self.device)  # Add channel dimension and ensure float
            
            self.optimizer.zero_grad()
            
            if self.args.mixed_precision:
                with torch.cuda.amp.autocast():
                    # Forward pass - PSPNet returns (main_output, aux_output) in training mode
                    main_output, aux_output = self.model(images)
                    
                    # Calculate losses
                    main_loss, _ = self.criterion(main_output, masks)
                    aux_loss, _ = self.criterion(aux_output, masks)
                    
                    # Combined loss with auxiliary weight
                    total_loss = main_loss + self.args.aux_weight * aux_loss
                
                # Backward pass
                self.scaler.scale(total_loss).backward()
                
                if self.args.gradient_clip_val > 0:
                    self.scaler.unscale_(self.optimizer)
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.args.gradient_clip_val)
                
                self.scaler.step(self.optimizer)
                self.scaler.update()
            else:
                # Forward pass
                main_output, aux_output = self.model(images)
                
                # Calculate losses
                main_loss, _ = self.criterion(main_output, masks)
                aux_loss, _ = self.criterion(aux_output, masks)
                
                # Combined loss
                total_loss = main_loss + self.args.aux_weight * aux_loss
                
                # Backward pass
                total_loss.backward()
                
                if self.args.gradient_clip_val > 0:
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.args.gradient_clip_val)
                
                self.optimizer.step()
            
            # Update running losses
            running_loss += total_loss.item()
            running_main_loss += main_loss.item()
            running_aux_loss += aux_loss.item()
            
            # Log progress
            if batch_idx % 100 == 0:
                self.logger.info(
                    f"Epoch {epoch}, Batch {batch_idx}/{len(self.train_loader)}: "
                    f"Total Loss: {total_loss.item():.6f}, "
                    f"Main Loss: {main_loss.item():.6f}, "
                    f"Aux Loss: {aux_loss.item():.6f}"
                )
        
        avg_loss = running_loss / len(self.train_loader)
        avg_main_loss = running_main_loss / len(self.train_loader)
        avg_aux_loss = running_aux_loss / len(self.train_loader)
        
        return avg_loss, avg_main_loss, avg_aux_loss
    
    def validate_epoch(self, epoch):
        """Validate for one epoch"""
        self.model.eval()
        running_loss = 0.0
        all_ious = []
        
        with torch.no_grad():
            for batch_idx, (images, masks) in enumerate(self.val_loader):
                images = images.to(self.device)
                masks = masks.float().unsqueeze(1).to(self.device)  # Add channel dimension and ensure float
                
                if self.args.mixed_precision:
                    with torch.cuda.amp.autocast():
                        # In eval mode, PSPNet returns only main output
                        outputs = self.model(images)
                        loss, _ = self.criterion(outputs, masks)
                else:
                    outputs = self.model(images)
                    loss, _ = self.criterion(outputs, masks)
                
                running_loss += loss.item()
                
                # Calculate metrics
                preds = torch.sigmoid(outputs)
                metrics = calculate_metrics(preds, masks)
                all_ious.append(metrics['iou'])
        
        avg_loss = running_loss / len(self.val_loader)
        avg_iou = np.mean(all_ious)
        
        return avg_loss, avg_iou
    
    def save_checkpoint(self, epoch, is_best=False):
        """Save model checkpoint"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.module.state_dict() if hasattr(self.model, 'module') else self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict() if self.scheduler else None,
            'best_iou': self.best_iou,
            'best_loss': self.best_loss,
            'args': self.args
        }
        
        # Save regular checkpoint
        checkpoint_path = Path(self.args.output_dir) / f'checkpoint_epoch_{epoch}.pth'
        torch.save(checkpoint, checkpoint_path)
        
        # Save best model
        if is_best:
            best_path = Path(self.args.output_dir) / 'best_model.pth'
            torch.save(checkpoint, best_path)
            self.logger.info(f"New best model saved: IoU = {self.best_iou:.4f}")
    
    def train(self):
        """Main training loop"""
        self.logger.info("Starting PSPNet training...")
        self.logger.info(f"Training for {self.args.epochs} epochs")
        
        for epoch in range(1, self.args.epochs + 1):
            self.logger.info(f"\nEpoch {epoch}/{self.args.epochs}")
            
            # Training
            train_loss, train_main_loss, train_aux_loss = self.train_epoch(epoch)
            
            # Validation
            val_loss, val_iou = self.validate_epoch(epoch)
            
            # Scheduler step
            if self.scheduler:
                if self.args.scheduler == 'reduce':
                    self.scheduler.step(val_iou)
                else:
                    self.scheduler.step()
            
            # Check for improvement
            is_best = val_iou > self.best_iou
            if is_best:
                self.best_iou = val_iou
                self.best_loss = val_loss
                self.patience_counter = 0
            else:
                self.patience_counter += 1
            
            # Log epoch results
            current_lr = self.optimizer.param_groups[0]['lr']
            self.logger.info(
                f"Epoch {epoch} Results: "
                f"Train Loss: {train_loss:.6f} (Main: {train_main_loss:.6f}, Aux: {train_aux_loss:.6f}), "
                f"Val Loss: {val_loss:.6f}, "
                f"Val IoU: {val_iou:.4f}, "
                f"LR: {current_lr:.2e}, "
                f"Best IoU: {self.best_iou:.4f}"
            )
            
            # Save checkpoint
            if epoch % self.args.save_every == 0 or is_best:
                self.save_checkpoint(epoch, is_best)
            
            # Early stopping
            if self.patience_counter >= self.args.patience:
                self.logger.info(f"Early stopping triggered after {epoch} epochs")
                break
        
        self.logger.info("Training completed!")
        self.logger.info(f"Best IoU: {self.best_iou:.4f}")

def main():
    parser = argparse.ArgumentParser(description='PSPNet New Architecture Training')
    
    # Data arguments
    parser.add_argument('--dataset_path', type=str, required=True, help='Path to dataset')
    parser.add_argument('--output_dir', type=str, required=True, help='Output directory')
    parser.add_argument('--img_size', type=int, default=1024, help='Image size')
    
    # Training arguments
    parser.add_argument('--epochs', type=int, default=25, help='Number of epochs')
    parser.add_argument('--batch_size', type=int, default=6, help='Batch size')
    parser.add_argument('--lr', type=float, default=3e-5, help='Learning rate')
    parser.add_argument('--weight_decay', type=float, default=1e-4, help='Weight decay')
    parser.add_argument('--optimizer', type=str, default='adamw', choices=['adam', 'adamw'])
    parser.add_argument('--scheduler', type=str, default='cosine', choices=['cosine', 'reduce', 'none'])
    parser.add_argument('--min_lr', type=float, default=1e-7, help='Minimum learning rate')
    parser.add_argument('--patience', type=int, default=20, help='Early stopping patience')
    
    # Loss arguments
    parser.add_argument('--focal_weight', type=float, default=2.0, help='Focal loss weight')
    parser.add_argument('--dice_weight', type=float, default=1.0, help='Dice loss weight')
    parser.add_argument('--iou_weight', type=float, default=1.0, help='IoU loss weight')
    parser.add_argument('--aux_weight', type=float, default=0.4, help='Auxiliary loss weight')
    
    # Model arguments
    parser.add_argument('--use_instance_norm', action='store_true', help='Use instance normalization')
    parser.add_argument('--mixed_precision', action='store_true', help='Use mixed precision training')
    parser.add_argument('--gradient_clip_val', type=float, default=1.0, help='Gradient clipping value')
    
    # Data loading arguments
    parser.add_argument('--num_workers', type=int, default=6, help='Number of data loading workers')
    parser.add_argument('--augment_prob', type=float, default=0.7, help='Data augmentation probability')
    
    # Checkpoint arguments
    parser.add_argument('--save_every', type=int, default=5, help='Save checkpoint every N epochs')
    
    args = parser.parse_args()
    
    # Create output directory
    Path(args.output_dir).mkdir(parents=True, exist_ok=True)
    
    # Save arguments
    with open(Path(args.output_dir) / 'args.json', 'w') as f:
        json.dump(vars(args), f, indent=2)
    
    # Start training
    trainer = PSPNetTrainer(args)
    trainer.train()

if __name__ == '__main__':
    main()
