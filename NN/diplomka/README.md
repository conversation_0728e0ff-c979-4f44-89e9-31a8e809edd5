# Spheroid Segmentation Project

Medical image segmentation project for binary segmentation of cellular spheroids from microscopy images.

## Quick Start

1. **Validate your dataset:**
   ```bash
   bash scripts/validation/validate_dataset.sh /path/to/your/dataset
   ```

2. **Train a model with automatic learning rate finding:**
   ```bash
   cd scripts/training
   bash train_hrnet.sh
   ```

3. **Evaluate the trained model:**
   ```bash
   python scripts/evaluation/evaluate_model.py \
       --model_path outputs/hrnet_spheroid/best_model.pth \
       --dataset_path /path/to/your/dataset \
       --model_name hrnet \
       --use_tta
   ```

## Project Structure

- `CNN_main_spheroid.py` - Main training script
- `models/` - Neural network architectures
- `scripts/` - Organized scripts for different tasks
  - `training/` - Model training scripts
  - `evaluation/` - Model evaluation tools
  - `validation/` - Dataset validation utilities
  - `utilities/` - Helper scripts
- `configs/` - Configuration files
- `docs/` - Detailed documentation

## Features

- **Multi-GPU training** with DistributedDataParallel
- **Automatic learning rate finding**
- **Enhanced early stopping** with best model tracking
- **Test Time Augmentation** for improved accuracy
- **Support for multiple image formats** (PNG, JPG, TIF, BMP, etc.)
- **Instance Normalization** for better microscopy data handling

## Models

1. **ResUNet** - Enhanced U-Net with attention gates
2. **HRNet** - High-Resolution Network (recommended)
3. **PSPNet** - Pyramid Scene Parsing Network
4. **TransUNet** - Transformer + U-Net hybrid

## Requirements

See `requirements_spheroid.txt` for dependencies.

## Documentation

For detailed information, see:
- `CLAUDE.md` - Technical guide for development
- `docs/SPHEROID_SEGMENTATION_GUIDE.md` - Comprehensive training guide