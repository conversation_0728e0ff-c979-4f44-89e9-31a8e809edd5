#!/usr/bin/env python3
"""
Test nové PSPNet architektury na malém vzorku dat
"""

import torch
import sys
import os
sys.path.append('/home/<USER>/SpheroSeg/NN/diplomka')

from models.pspnet_new import PSPNet as NewPSPNet
import numpy as np
import cv2
from pathlib import Path

def test_new_pspnet():
    print("=== Test nové PSPNet architektury ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Používám zařízení: {device}")
    
    # Vytvoření modelu
    model = NewPSPNet(n_class=1, backbone='resnet101', pretrained=True, use_instance_norm=True)
    model.to(device)
    model.eval()
    
    # Počet parametrů
    total_params = sum(p.numel() for p in model.parameters())
    print(f"Celkový počet parametrů: {total_params:,} ({total_params/1_000_000:.2f}M)")
    
    # Test na náhodných datech
    print("\n=== Test na náhodných datech ===")
    dummy_input = torch.randn(1, 3, 1024, 1024).to(device)
    
    # Test v eval režimu
    with torch.no_grad():
        start_time = torch.cuda.Event(enable_timing=True)
        end_time = torch.cuda.Event(enable_timing=True)
        
        start_time.record()
        output = model(dummy_input)
        end_time.record()
        
        torch.cuda.synchronize()
        inference_time = start_time.elapsed_time(end_time) / 1000.0  # Convert to seconds
    
    print(f"Výstupní tvar: {output.shape}")
    print(f"Čas inference: {inference_time:.4f}s")
    print(f"Rozsah výstupních hodnot: [{output.min().item():.4f}, {output.max().item():.4f}]")
    
    # Test v train režimu (měl by vrátit dva výstupy)
    print("\n=== Test v train režimu ===")
    model.train()
    with torch.no_grad():
        main_out, aux_out = model(dummy_input)
    
    print(f"Hlavní výstup: {main_out.shape}")
    print(f"Pomocný výstup: {aux_out.shape}")
    
    # Test na skutečných datech (pokud jsou dostupné)
    test_image_path = "/data/prusek/training_small/test/images"
    if os.path.exists(test_image_path):
        print(f"\n=== Test na skutečných datech ===")
        image_files = list(Path(test_image_path).glob("*.bmp"))[:3]  # Pouze 3 obrázky
        
        model.eval()
        for img_path in image_files:
            # Načtení a předzpracování obrázku
            image = cv2.imread(str(img_path))
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            image = cv2.resize(image, (1024, 1024))
            image = image.astype(np.float32) / 255.0
            image_tensor = torch.from_numpy(image).permute(2, 0, 1).unsqueeze(0).to(device)
            
            # Inference
            with torch.no_grad():
                start_time = torch.cuda.Event(enable_timing=True)
                end_time = torch.cuda.Event(enable_timing=True)
                
                start_time.record()
                output = model(image_tensor)
                end_time.record()
                
                torch.cuda.synchronize()
                inference_time = start_time.elapsed_time(end_time) / 1000.0
            
            # Aplikace sigmoid pro získání pravděpodobností
            prob_map = torch.sigmoid(output).cpu().numpy()[0, 0]
            
            print(f"  {img_path.name}:")
            print(f"    Čas inference: {inference_time:.4f}s")
            print(f"    Rozsah pravděpodobností: [{prob_map.min():.4f}, {prob_map.max():.4f}]")
            print(f"    Průměrná pravděpodobnost: {prob_map.mean():.4f}")
    
    print("\n✓ Test nové PSPNet architektury úspěšně dokončen!")
    
    # Uvolnění GPU paměti
    del model
    torch.cuda.empty_cache()

if __name__ == "__main__":
    test_new_pspnet()
