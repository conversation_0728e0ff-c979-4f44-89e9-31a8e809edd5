"""Detailed debugging of U-Mamba architecture"""

import torch
import torch.nn as nn

# Let's manually trace through the architecture

features = [64, 128, 256, 512, 1024]
print("Features:", features)

# Simulate encoder outputs and skip connections
print("\n=== ENCODER SIMULATION ===")
encoder_outputs = []
skip_connections = []
current_h_w = 256  # Starting spatial dimension

for i, feat in enumerate(features):
    print(f"\nEncoder {i}: output channels = {feat}")
    print(f"  Spatial size: {current_h_w} x {current_h_w}")
    
    # Skip connection before pooling
    skip_connections.append((feat, current_h_w))
    
    # Pooling
    current_h_w = current_h_w // 2
    encoder_outputs.append((feat, current_h_w))

print("\n=== COLLECTED SKIP CONNECTIONS ===")
for i, (channels, size) in enumerate(skip_connections):
    print(f"Skip {i}: {channels} channels, {size}x{size} spatial")

print("\n=== BOTTLENECK ===")
print(f"Input/Output: {features[-1]} channels, {current_h_w}x{current_h_w} spatial")

print("\n=== DECODER CONSTRUCTION (from umamba.py) ===")
# This is how decoders are constructed in the model
for i in range(len(features) - 1):
    in_channels = features[-(i+1)]  # 1024, 512, 256, 128
    skip_channels = features[-(i+2)]  # 512, 256, 128, 64
    out_channels = features[-(i+2)]  # 512, 256, 128, 64
    
    print(f"\nDecoder {i}:")
    print(f"  in_channels: {in_channels}")
    print(f"  skip_channels: {skip_channels}")
    print(f"  out_channels: {out_channels}")
    print(f"  After upsample: {in_channels} -> {in_channels // 2}")
    print(f"  After concat: {in_channels // 2} + {skip_channels} = {in_channels // 2 + skip_channels}")

print("\n=== DECODER FLOW (with fixed skip connections) ===")
# Skip connections for decoder (excluding last encoder block)
decoder_skips = skip_connections[:-1][::-1]  # Reverse, excluding last
print("\nSkip connections for decoder (reversed, excluding last):")
for i, (channels, size) in enumerate(decoder_skips):
    print(f"  Skip {i}: {channels} channels, {size}x{size} spatial")

# Simulate decoder flow
current_channels = features[-1]  # 1024 from bottleneck
current_size = encoder_outputs[-1][1]  # 8x8 from bottleneck

print(f"\nStarting from bottleneck: {current_channels} channels, {current_size}x{current_size}")

for i, (skip_channels, skip_size) in enumerate(decoder_skips):
    print(f"\nDecoder {i}:")
    print(f"  Current: {current_channels} channels, {current_size}x{current_size}")
    
    # Upsample
    upsampled_channels = current_channels // 2
    upsampled_size = current_size * 2
    print(f"  After upsample: {upsampled_channels} channels, {upsampled_size}x{upsampled_size}")
    
    # Check spatial alignment
    print(f"  Skip connection: {skip_channels} channels, {skip_size}x{skip_size}")
    if upsampled_size != skip_size:
        print(f"  ERROR: Spatial mismatch! {upsampled_size} != {skip_size}")
    
    # Concatenate
    concat_channels = upsampled_channels + skip_channels
    print(f"  After concat: {concat_channels} channels")
    
    # Expected by decoder conv
    expected = features[-(i+2)]  # This is how decoder calculates expected input
    print(f"  Decoder conv expects: {upsampled_channels} + {expected} = {upsampled_channels + expected} channels")
    
    if concat_channels != upsampled_channels + expected:
        print(f"  ERROR: Channel mismatch! {concat_channels} != {upsampled_channels + expected}")
    
    # Output of decoder block
    current_channels = expected
    current_size = upsampled_size