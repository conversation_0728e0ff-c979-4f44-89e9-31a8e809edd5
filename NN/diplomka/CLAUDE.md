# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## CRITICAL: AUTOMATIC SERVER SYNCHRONIZATION
**AFTER EVERY CODE CHANGE, YOU MUST IMMEDIATELY SYNCHRONIZE WITH THE TRAINING SERVER!**

This is not optional - the code runs on the remote server, not locally. Without syncing, changes will not take effect.

### Sync Details:
- Server: <EMAIL>
- Path: /home/<USER>/SpheroSeg/NN/diplomka
- Password: M1I2C3H4A5L6
- Sync command: `rsync -avz --exclude='.git' --exclude='__pycache__' --exclude='*.pyc' --exclude='outputs' --exclude='.cache' ./ <EMAIL>:/home/<USER>/SpheroSeg/NN/diplomka/`

### When to Sync:
- After EVERY file edit
- After EVERY new file creation
- After EVERY file deletion
- Before confirming any task is complete

**Remember: If you don't sync, the changes don't exist on the server where the code actually runs!**

## Project Overview
Medical image segmentation project focused on binary segmentation of cellular spheroids from microscopy images. The project implements multiple state-of-the-art architectures optimized for high-resolution (1024×1024) spheroid segmentation using 2x NVIDIA L40S GPUs.

## File Organization

### Core Components
```
├── CNN_main_spheroid.py      # Main training pipeline with multi-GPU support
├── models/                   # Model architectures
│   ├── resunet.py           # Enhanced ResUNet
│   ├── hrnet.py             # High-Resolution Network
│   ├── pspnet.py            # Pyramid Scene Parsing Network
│   └── transunet.py         # Transformer + U-Net hybrid
├── scripts/
│   ├── training/            # Training scripts
│   │   ├── train_hrnet.sh
│   │   ├── train_transunet.sh
│   │   ├── train_resunet.sh
│   │   └── train_pspnet.sh
│   ├── evaluation/          # Evaluation tools
│   │   └── evaluate_model.py
│   ├── validation/          # Dataset validation
│   │   ├── check_dataset_formats.py
│   │   ├── test_dataset_loading.py
│   │   └── validate_dataset.sh
│   └── utilities/           # Utility scripts
│       ├── compare_model_capacity.py
│       └── run_model_comparison.sh
├── configs/                 # Configuration files
│   └── recommended_lr.json  # Empirical learning rates
└── docs/                    # Documentation
    └── SPHEROID_SEGMENTATION_GUIDE.md
```

## Key Features

### Training Infrastructure
- **Multi-GPU Support**: DistributedDataParallel (DDP) for 2x NVIDIA L40S
- **Learning Rate Finder**: Automatic optimal LR detection with `--find_lr`
- **Enhanced Early Stopping**: Tracks best model with patience and min_delta
- **Mixed Precision**: AMP for memory efficiency
- **Test Time Augmentation**: 8 augmentations for better inference
- **Instance Normalization**: Better for microscopy data

### Loss Functions
- Combined loss: Focal + Dice + IoU + optional Boundary loss
- Configurable weights for each component
- Boundary loss improves edge detection (--boundary_weight 0.1)

### Data Support
- Multiple image formats: PNG, JPG, JPEG, TIF, TIFF, BMP
- Automatic image-mask pairing validation
- Detailed dataset statistics at startup

## Common Commands

### Training Models
```bash
# Train with automatic LR finding
python CNN_main_spheroid.py \
    --dataset_path /path/to/training_big \
    --model hrnet \
    --find_lr \
    --patience 15 \
    --min_delta 1e-4 \
    --gpus 2

# Train with dataset caching for faster loading
python CNN_main_spheroid.py \
    --dataset_path /path/to/training_big \
    --model hrnet \
    --use_cache \
    --batch_size 16

# Train with boundary loss for better edges
python CNN_main_spheroid.py \
    --dataset_path /path/to/training_big \
    --model hrnet \
    --boundary_weight 0.1

# Train with recommended settings
bash scripts/training/train_hrnet.sh
```

### Dataset Validation
```bash
# Complete validation before training
bash scripts/validation/validate_dataset.sh /path/to/dataset

# Check formats only
python scripts/validation/check_dataset_formats.py --dataset_path /path/to/dataset
```

### Model Evaluation
```bash
python scripts/evaluation/evaluate_model.py \
    --model_path outputs/best_model.pth \
    --dataset_path /path/to/dataset \
    --model_name hrnet \
    --use_tta \
    --find_threshold
```

### Model Comparison
```bash
bash scripts/utilities/run_model_comparison.sh
```

## Recommended Learning Rates
Based on empirical testing (see configs/recommended_lr.json):
- **ResUNet**: 1e-3 (AdamW), 1e-2 (SGD)
- **HRNet**: 5e-4 (AdamW), 5e-3 (SGD)
- **PSPNet**: 1e-3 (AdamW), 1e-2 (SGD)
- **TransUNet**: 1e-3 (AdamW), 5e-3 (SGD)

## Dataset Structure
```
training_big/
├── train/
│   ├── images/    # Supported formats: PNG, JPG, JPEG, TIF, TIFF, BMP
│   └── masks/     # Binary masks (any supported format)
├── val/
│   ├── images/
│   └── masks/
└── test/
    ├── images/
    └── masks/
```

**Important**: 
- Images without corresponding masks are automatically excluded
- Dataset reports missing masks and final counts at startup
- Image and mask can have different formats

## Training Best Practices
1. Run dataset validation first: `bash scripts/validation/validate_dataset.sh`
2. Use `--find_lr` for first training to find optimal learning rate
3. Monitor early stopping messages for best epoch
4. Use TTA during evaluation for better results
5. Default batch sizes are optimized for L40S (48GB) memory

## Technical Specifications
- **Input Size**: 1024×1024 pixels
- **Normalization**: Instance Norm (better for varying microscopy conditions)
- **Augmentations**: Rotation, elastic transform, CLAHE, optical distortion
- **Model Capacity**: All models ~40-60M parameters for fair comparison
- **GPU Memory**: Optimized for 2x NVIDIA L40S (48GB each)

## Recent Improvements (Based on External Recommendations)

### Critical Fixes Applied
1. **OneCycleLR Scheduler**: Fixed to update per batch instead of per epoch
2. **TTA Loss Calculation**: Fixed double sigmoid application issue
3. **Normalization Consistency**: All models now properly use Instance/Batch Normalization
4. **Boundary Loss**: Added optional boundary loss for better edge detection
5. **Dataset Caching**: Added optional caching mechanism for faster data loading

### Implementation Notes
- Attention gates in ResUNet keep BatchNorm for stability
- HRNet transition layers now respect use_instance_norm flag
- Loss components logging now includes boundary loss when enabled
- Cache files stored in dataset_dir/.cache/[split]/ directory