"""Debug script to trace U-Mamba architecture flow and understand channel sizes"""

import torch
from models.umamba import UMamba

def trace_umamba_flow():
    """Trace the flow through U-Mamba to understand skip connection sizes"""
    
    # Create model with default features
    features = [64, 128, 256, 512, 1024]
    model = UMamba(in_channels=3, out_channels=1, features=features)
    
    # Create dummy input
    x = torch.randn(1, 3, 256, 256)
    
    print("=== U-Mamba Architecture Flow Analysis ===\n")
    print(f"Initial input shape: {x.shape}")
    print(f"Features: {features}")
    
    # Manually trace through encoder
    print("\n=== ENCODER PATH ===")
    skip_connections = []
    current = x
    
    for i, encoder in enumerate(model.encoder_blocks):
        in_channels = [3] + features[:-1]
        out_channels = features[i]
        print(f"\nEncoder Block {i}:")
        print(f"  Input channels: {in_channels[i]} -> Output channels: {out_channels}")
        print(f"  Input shape: {current.shape}")
        
        # Apply encoder block
        current, skip = encoder(current)
        skip_connections.append(skip)
        
        print(f"  Skip connection shape: {skip.shape}")
        print(f"  Output shape (after pooling): {current.shape}")
    
    # Bottleneck
    print("\n=== BOTTLENECK ===")
    print(f"Input shape: {current.shape}")
    current = model.bottleneck(current)
    if model.bottleneck_mamba is not None:
        current = model.bottleneck_mamba(current)
    print(f"Output shape: {current.shape}")
    
    # Reverse skip connections for decoder
    skip_connections_reversed = skip_connections[::-1]
    
    print("\n=== DECODER PATH ===")
    print("\nSkip connections (reversed for decoder):")
    for i, skip in enumerate(skip_connections_reversed):
        print(f"  Skip {i}: shape {skip.shape}, channels {skip.shape[1]}")
    
    # Trace through decoder
    for i, (decoder, skip) in enumerate(zip(model.decoder_blocks, skip_connections_reversed)):
        print(f"\nDecoder Block {i}:")
        print(f"  Current shape before upsampling: {current.shape}")
        
        # Calculate expected channels
        decoder_idx = len(features) - 1 - i
        in_channels = features[decoder_idx]  # Current decoder input
        skip_channels = features[decoder_idx - 1] if decoder_idx > 0 else features[0]  # Skip from encoder
        out_channels = skip_channels  # Output matches skip
        
        print(f"  Expected configuration:")
        print(f"    - in_channels: {in_channels}")
        print(f"    - skip_channels: {skip_channels}")
        print(f"    - out_channels: {out_channels}")
        
        # Check actual decoder configuration
        print(f"  Actual decoder conv expects: {decoder.conv.double_conv[0].in_channels} channels")
        
        # Simulate upsampling
        up_channels = in_channels // 2
        print(f"  After upsampling: {current.shape[1]} -> {up_channels} channels")
        print(f"  After concat with skip: {up_channels} + {skip.shape[1]} = {up_channels + skip.shape[1]} channels")
        
        # Apply decoder
        current = decoder(current, skip)
        print(f"  Output shape: {current.shape}")
    
    # Final conv
    print("\n=== FINAL CONVOLUTION ===")
    print(f"Input shape: {current.shape}")
    output = model.final_conv(current)
    print(f"Output shape: {output.shape}")
    
    # Analyze the issue
    print("\n=== ISSUE ANALYSIS ===")
    print("\nThe problem was in the skip connection collection:")
    print("- The last encoder block output (1024 channels) shouldn't create a skip connection")
    print("- It goes directly to the bottleneck")
    print("- The decoder should only use skip connections from the first N-1 encoder blocks")
    print("\nCorrect pairing should be:")
    print("- Decoder 0: bottleneck (1024) -> upsample to 512 + skip from encoder 3 (512) = 1024")
    print("- Decoder 1: decoder 0 (512) -> upsample to 256 + skip from encoder 2 (256) = 512")
    print("- Decoder 2: decoder 1 (256) -> upsample to 128 + skip from encoder 1 (128) = 256")
    print("- Decoder 3: decoder 2 (128) -> upsample to 64 + skip from encoder 0 (64) = 128")

if __name__ == "__main__":
    trace_umamba_flow()