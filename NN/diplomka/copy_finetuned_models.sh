#!/bin/bash

# Skript pro kopírování kompletních finetuned modelů ze serveru
# Kopíruje modely, logy, tensorboard data, training skripty a architektury

SERVER="<EMAIL>"
SERVER_PATH="/home/<USER>/SpheroSeg/NN/diplomka/scripts/training/outputs"
SERVER_ROOT="/home/<USER>/SpheroSeg/NN/diplomka"
LOCAL_DEST="$HOME/Desktop/SpheroSeg_trained"
PASSWORD="M1I2C3H4A5L6"

# Pole s názvy finetuned složek
FINETUNED_FOLDERS=(
    "hrnet_finetuned"
    "pspnet_finetuned" 
    "resunet_finetuned"
    "transunet_optimized"
)

echo "Vytvářím cílovou složku: $LOCAL_DEST"
mkdir -p "$LOCAL_DEST"
mkdir -p "$LOCAL_DEST/models"
mkdir -p "$LOCAL_DEST/scripts"

echo "Kopíruji finetuned modely a související data ze serveru..."

# Kopíruj model architektury
echo "Kopíruji model architektury..."
sshpass -p "$PASSWORD" scp -r "$SERVER:$SERVER_ROOT/models/" "$LOCAL_DEST/"
echo "✓ Model architektury zkopírovány"

# Kopíruj training skripty
echo "Kopíruji training skripty..."
sshpass -p "$PASSWORD" scp -r "$SERVER:$SERVER_ROOT/scripts/" "$LOCAL_DEST/"
echo "✓ Training skripty zkopírovány"

# Kopíruj hlavní training soubor
echo "Kopíruji hlavní training soubor..."
sshpass -p "$PASSWORD" scp "$SERVER:$SERVER_ROOT/CNN_main_spheroid.py" "$LOCAL_DEST/"
echo "✓ CNN_main_spheroid.py zkopírován"

# Kopíruj konfigurace
echo "Kopíruji konfigurace..."
sshpass -p "$PASSWORD" scp -r "$SERVER:$SERVER_ROOT/configs/" "$LOCAL_DEST/" 2>/dev/null
echo "✓ Konfigurace zkopírovány"

for folder in "${FINETUNED_FOLDERS[@]}"; do
    echo ""
    echo "Zpracovávám: $folder"
    
    # Vytvoř lokální složku pro model
    mkdir -p "$LOCAL_DEST/trained_models/$folder"
    
    # Kopíruj celou složku modelu (včetně logů, tensorboard, checkpointů)
    echo "Kopíruji kompletní složku $folder..."
    sshpass -p "$PASSWORD" scp -r "$SERVER:$SERVER_PATH/$folder/*" "$LOCAL_DEST/trained_models/$folder/" 2>/dev/null
    
    if [ $? -eq 0 ]; then
        echo "✓ Úspěšně zkopírován: $folder (kompletní složka)"
    else
        echo "✗ Chyba při kopírování: $folder"
    fi
    
    # Zkontroluj a vypíš obsah
    if [ -d "$LOCAL_DEST/trained_models/$folder" ]; then
        echo "   Obsah $folder:"
        ls -la "$LOCAL_DEST/trained_models/$folder" | grep -v "^total" | sed 's/^/   /'
    fi
done

echo ""
echo "Kopírování dokončeno!"
echo "Kompletní training setup je uložen v: $LOCAL_DEST"
echo ""
echo "Celková struktura:"
echo "├── models/                    # Model architektury"
echo "├── scripts/                  # Training a evaluation skripty"
echo "├── configs/                  # Konfigurace"
echo "├── CNN_main_spheroid.py     # Hlavní training soubor"
echo "└── trained_models/          # Natrénované modely s logy"
for folder in "${FINETUNED_FOLDERS[@]}"; do
    echo "    ├── $folder/"
done