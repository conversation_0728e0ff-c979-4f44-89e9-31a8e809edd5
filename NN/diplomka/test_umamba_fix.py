"""Test U-Mamba fix"""

import torch
from models.umamba import UMamba

# Test the model
model = UMamba(in_channels=3, out_channels=1, features=[64, 128, 256, 512, 1024])
x = torch.randn(1, 3, 256, 256)

try:
    output = model(x)
    print("SUCCESS! Model forward pass completed.")
    print(f"Input shape: {x.shape}")
    print(f"Output shape: {output.shape}")
except Exception as e:
    print(f"ERROR: {e}")