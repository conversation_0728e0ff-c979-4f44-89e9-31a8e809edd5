#!/usr/bin/env python3
"""
<PERSON><PERSON>t to create a smaller training dataset by copying training_big to training_small
and removing images/masks that start with specific prefixes.
"""

import os
import shutil
import glob
from pathlib import Path

def copy_dataset(source_dir, target_dir):
    """Copy the entire dataset structure from source to target."""
    print(f"Copying {source_dir} to {target_dir}...")
    
    # Remove target directory if it exists
    if os.path.exists(target_dir):
        shutil.rmtree(target_dir)
    
    # Copy the entire directory
    shutil.copytree(source_dir, target_dir)
    print(f"Successfully copied dataset to {target_dir}")

def remove_files_by_prefix(dataset_dir, prefixes):
    """Remove images and masks that start with specified prefixes."""
    train_dir = os.path.join(dataset_dir, "train")
    
    removed_count = 0
    
    for split in ["images", "masks"]:
        split_dir = os.path.join(train_dir, split)
        if not os.path.exists(split_dir):
            continue
            
        print(f"Processing {split_dir}...")
        
        # Get all files in the directory
        files = os.listdir(split_dir)
        
        for file in files:
            file_path = os.path.join(split_dir, file)
            
            # Check if file starts with any of the prefixes
            for prefix in prefixes:
                if file.startswith(prefix):
                    print(f"Removing: {file}")
                    os.remove(file_path)
                    removed_count += 1
                    break
    
    print(f"Removed {removed_count} files total")

def main():
    # Define paths
    source_dir = "/data/prusek/training_big"
    target_dir = "/data/prusek/training_small"
    
    # Define prefixes to remove
    prefixes_to_remove = ["slimia", "SpheroidJ", "Deep-Tumour-Spheroid"]
    
    print("Creating training_small dataset...")
    print(f"Source: {source_dir}")
    print(f"Target: {target_dir}")
    print(f"Prefixes to remove: {prefixes_to_remove}")
    
    # Step 1: Copy the dataset
    copy_dataset(source_dir, target_dir)
    
    # Step 2: Remove files with specified prefixes
    remove_files_by_prefix(target_dir, prefixes_to_remove)
    
    # Step 3: Show final statistics
    train_images_dir = os.path.join(target_dir, "train", "images")
    train_masks_dir = os.path.join(target_dir, "train", "masks")
    
    if os.path.exists(train_images_dir):
        image_count = len(os.listdir(train_images_dir))
        print(f"Final training images count: {image_count}")
    
    if os.path.exists(train_masks_dir):
        mask_count = len(os.listdir(train_masks_dir))
        print(f"Final training masks count: {mask_count}")
    
    print("Dataset creation completed!")

if __name__ == "__main__":
    main()