"""Debug the decoder construction in U-Mamba"""

features = [64, 128, 256, 512, 1024]

print("=== DECODER CONSTRUCTION IN U-MAMBA ===")
print("Code from lines 303-314:\n")

for i in range(len(features) - 1):  # 0, 1, 2, 3
    # Decoder processes from deep to shallow
    in_channels = features[-(i+1)]  # features[-1], features[-2], features[-3], features[-4]
    skip_channels = features[-(i+2)]  # features[-2], features[-3], features[-4], features[-5]
    out_channels = features[-(i+2)]  
    
    print(f"Decoder {i}:")
    print(f"  in_channels = features[{-(i+1)}] = {in_channels}")
    print(f"  skip_channels = features[{-(i+2)}] = {skip_channels}")
    print(f"  Upsample: {in_channels} -> {in_channels // 2}")
    print(f"  Expected input to conv: {in_channels // 2} + {skip_channels} = {in_channels // 2 + skip_channels}")
    print()

print("\n=== SKIP CONNECTIONS WE'RE PROVIDING ===")
skip_connections = features  # [64, 128, 256, 512, 1024]
decoder_skips = skip_connections[1:][::-1]  # [1024, 512, 256, 128]

for i, skip in enumerate(decoder_skips):
    print(f"Decoder {i} receives skip with {skip} channels")

print("\n=== MISMATCH ANALYSIS ===")
print("Decoder 0:")
print("  Expects skip with 512 channels")
print("  Receives skip with 1024 channels")
print("  This causes the error!")