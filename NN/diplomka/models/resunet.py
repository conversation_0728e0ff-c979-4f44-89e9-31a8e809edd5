# ResUnet.py

import torch
import torch.nn as nn
import torchvision.transforms.functional as TF


# ===========================
# Normalization Helper
# ===========================
def get_norm_layer(num_features, use_instance_norm=True):
    """Get normalization layer (Instance or Batch)"""
    if use_instance_norm:
        return nn.InstanceNorm2d(num_features, affine=True)
    else:
        return nn.BatchNorm2d(num_features)


# ===========================
# Squeeze-and-Excitation Block
# ===========================
class SEBlock(nn.Module):
    """
    Squeeze-and-Excitation (SE) Block for channel-wise attention.
    """

    def __init__(self, in_channels, reduction=16):
        super(SEBlock, self).__init__()
        self.global_avg_pool = nn.AdaptiveAvgPool2d(1)
        self.fc = nn.Sequential(
            nn.Linear(in_channels, in_channels // reduction, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(in_channels // reduction, in_channels, bias=False),
            nn.Sigmoid(),
        )

    def forward(self, x):
        b, c, _, _ = x.size()
        y = self.global_avg_pool(x).view(b, c)
        y = self.fc(y).view(b, c, 1, 1)
        return x * y


# ===========================
# Attention Gate
# ===========================
class AttentionGate(nn.Module):
    """
    Attention Gate to focus on relevant features in skip connections.
    """

    def __init__(self, F_g, F_l, F_int, use_instance_norm=True):
        super(AttentionGate, self).__init__()
        self.W_g = nn.Sequential(
            nn.Conv2d(F_g, F_int, kernel_size=1, stride=1, padding=0, bias=True),
            nn.BatchNorm2d(F_int)
        )

        self.W_x = nn.Sequential(
            nn.Conv2d(F_l, F_int, kernel_size=1, stride=1, padding=0, bias=True),
            nn.BatchNorm2d(F_int)
        )

        self.psi = nn.Sequential(
            nn.Conv2d(F_int, 1, kernel_size=1, stride=1, padding=0, bias=True),
            nn.BatchNorm2d(1),
            nn.Sigmoid()
        )

        self.relu = nn.ReLU(inplace=True)

    def forward(self, g, x):
        """
        Args:
            g: Gating signal from the decoder (higher level features).
            x: Skip connection features from the encoder.
        """
        g1 = self.W_g(g)
        x1 = self.W_x(x)
        psi = self.relu(g1 + x1)
        psi = self.psi(psi)
        return x * psi


# ===========================
# Enhanced Residual Block with Improved Regularization
# ===========================
class ResidualBlock(nn.Module):
    def __init__(self, in_channels, out_channels, dilation=1, reduction=16, dropout=0.15, use_instance_norm=True):
        super(ResidualBlock, self).__init__()

        # First convolution path with enhanced regularization
        self.conv1 = nn.Conv2d(in_channels, out_channels, kernel_size=3, padding=dilation, dilation=dilation,
                               bias=False)
        self.norm1 = get_norm_layer(out_channels, use_instance_norm)
        self.relu = nn.ReLU(inplace=True)
        self.dropout1 = nn.Dropout2d(p=dropout * 0.5)  # Spatial dropout for first layer

        # Second convolution path
        self.conv2 = nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=dilation, dilation=dilation,
                               bias=False)
        self.norm2 = get_norm_layer(out_channels, use_instance_norm)
        self.dropout2 = nn.Dropout2d(p=dropout)  # Spatial dropout for second layer

        # Adjust residual connection if input and output channels differ
        self.adjust_channels = nn.Conv2d(in_channels, out_channels, kernel_size=1, padding=0,
                                         bias=False) if in_channels != out_channels else None

        # Normalization for skip connection if needed
        self.skip_norm = get_norm_layer(out_channels, use_instance_norm) if self.adjust_channels else None

        # Enhanced SE block with better regularization
        self.se = SEBlock(out_channels, reduction)

    def forward(self, x):
        # Enhanced residual connection with proper normalization
        residual = x
        if self.adjust_channels:
            residual = self.adjust_channels(x)
            if self.skip_norm:
                residual = self.skip_norm(residual)

        # Forward path with improved regularization
        out = self.conv1(x)
        out = self.norm1(out)
        out = self.relu(out)
        out = self.dropout1(out)

        out = self.conv2(out)
        out = self.norm2(out)
        out = self.dropout2(out)

        # Residual connection with SE attention
        out += residual
        out = self.relu(out)
        out = self.se(out)
        return out


# ===========================
# Enhanced ResUNet with Improved Generalization
# ===========================
class ResUNet(nn.Module):
    def __init__(self, in_channels=3, out_channels=1, features=[24, 48, 96, 192, 256],
                 use_instance_norm=True, dropout_rate=0.15, use_deep_supervision=False):
        super(ResUNet, self).__init__()
        self.use_instance_norm = use_instance_norm
        self.use_deep_supervision = use_deep_supervision
        self.ups = nn.ModuleList()
        self.downs = nn.ModuleList()
        self.attentions = nn.ModuleList()
        self.pool = nn.MaxPool2d(kernel_size=2, stride=2)

        # Initial convolution with regularization
        self.init_conv = nn.Sequential(
            nn.Conv2d(in_channels, features[0], kernel_size=3, stride=1, padding=1, bias=False),
            get_norm_layer(features[0], use_instance_norm),
            nn.ReLU(inplace=True),
            nn.Dropout2d(p=dropout_rate * 0.5)
        )

        # Down part of ResUNet with enhanced regularization
        in_features = features[0]
        for feature in features:
            self.downs.append(ResidualBlock(in_features, feature, dropout=dropout_rate,
                                          use_instance_norm=use_instance_norm))
            in_features = feature

        # Enhanced bottleneck with multiple residual blocks
        self.bottleneck = nn.Sequential(
            ResidualBlock(features[-1], features[-1] * 2, dropout=dropout_rate * 1.2,
                         use_instance_norm=use_instance_norm),
            ResidualBlock(features[-1] * 2, features[-1] * 2, dropout=dropout_rate * 1.2,
                         use_instance_norm=use_instance_norm)
        )

        # Up part of ResUNet with enhanced regularization
        reversed_features = list(reversed(features))
        for i, feature in enumerate(reversed_features):
            # First upsampling layer connects from bottleneck (features[-1] * 2 channels)
            if i == 0:
                # From bottleneck (512 channels) to feature (192 channels)
                self.ups.append(nn.ConvTranspose2d(features[-1] * 2, feature, kernel_size=2, stride=2))
            else:
                # From previous decoder layer (feature * 2 channels) to current feature
                prev_feature = reversed_features[i-1]
                self.ups.append(nn.ConvTranspose2d(prev_feature, feature, kernel_size=2, stride=2))

            # Decoder block takes concatenated features (feature + feature = feature * 2)
            self.ups.append(ResidualBlock(feature * 2, feature, dropout=dropout_rate,
                                        use_instance_norm=use_instance_norm))
            self.attentions.append(AttentionGate(F_g=feature, F_l=feature, F_int=feature // 2,
                                               use_instance_norm=use_instance_norm))

        # Final output layers with regularization
        self.final_conv = nn.Sequential(
            nn.Conv2d(features[0], features[0] // 2, kernel_size=3, padding=1, bias=False),
            get_norm_layer(features[0] // 2, use_instance_norm),
            nn.ReLU(inplace=True),
            nn.Dropout2d(p=dropout_rate * 0.5),
            nn.Conv2d(features[0] // 2, out_channels, kernel_size=1)
        )

        # Deep supervision outputs (optional)
        if use_deep_supervision:
            self.deep_outputs = nn.ModuleList([
                nn.Conv2d(feature, out_channels, kernel_size=1) for feature in features[:-1]
            ])

        # Initialize weights properly
        self._init_weights()

    def _init_weights(self):
        """Initialize weights using He initialization for better training stability"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, (nn.BatchNorm2d, nn.InstanceNorm2d)):
                if m.weight is not None:
                    nn.init.constant_(m.weight, 1)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.01)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)

    def forward(self, x):
        # Store original input size for proper upsampling
        input_size = x.shape[2:]

        # Initial convolution
        x = self.init_conv(x)
        skip_connections = []

        # Encoder path
        for down in self.downs:
            x = down(x)
            skip_connections.append(x)
            x = self.pool(x)

        # Bottleneck
        x = self.bottleneck(x)
        skip_connections = skip_connections[::-1]

        # Decoder path with attention gates
        deep_outputs = []
        for idx in range(0, len(self.ups), 2):
            x = self.ups[idx](x)
            skip_connection = skip_connections[idx // 2]

            # Ensure spatial dimensions match
            if x.shape[2:] != skip_connection.shape[2:]:
                x = TF.resize(x, size=skip_connection.shape[2:])

            # Apply attention gate
            attention = self.attentions[idx // 2](g=x, x=skip_connection)
            concat_skip = torch.cat((attention, x), dim=1)
            x = self.ups[idx + 1](concat_skip)

            # Store for deep supervision if enabled
            if self.use_deep_supervision and idx // 2 < len(self.deep_outputs):
                deep_out = self.deep_outputs[idx // 2](x)
                deep_out = TF.resize(deep_out, size=input_size)
                deep_outputs.append(deep_out)

        # Final output
        x = self.final_conv(x)

        # Ensure output matches input size
        if x.shape[2:] != input_size:
            x = TF.resize(x, size=input_size)

        if self.use_deep_supervision and self.training:
            return x, deep_outputs
        return x
