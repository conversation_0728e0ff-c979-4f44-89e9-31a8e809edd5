# Efficient TransUNet implementation with memory optimizations
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.utils.checkpoint as checkpoint
from einops import rearrange


def get_norm_layer(num_features, use_instance_norm=True):
    """Get normalization layer"""
    if use_instance_norm:
        return nn.InstanceNorm2d(num_features, affine=True)
    else:
        return nn.BatchNorm2d(num_features)


class EfficientPatchEmbedding(nn.Module):
    """More efficient patch embedding with smaller patches"""
    def __init__(self, img_size=64, patch_size=2, in_channels=768, embed_dim=768):
        super().__init__()
        self.img_size = img_size
        self.patch_size = patch_size
        self.n_patches = (img_size // patch_size) ** 2
        
        # Use larger patch size to reduce sequence length
        if patch_size == 1:
            self.proj = nn.Identity()
            self.rearrange = lambda x: rearrange(x, 'b c h w -> b (h w) c')
        else:
            self.proj = nn.Conv2d(in_channels, embed_dim, kernel_size=patch_size, stride=patch_size)
            self.rearrange = lambda x: rearrange(x, 'b c h w -> b (h w) c')
            
    def forward(self, x):
        x = self.proj(x)
        x = self.rearrange(x)
        return x


class EfficientMultiHeadAttention(nn.Module):
    """Memory-efficient multi-head attention"""
    def __init__(self, embed_dim, num_heads=12, dropout=0.1):
        super().__init__()
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads
        self.scale = self.head_dim ** -0.5
        
        self.qkv = nn.Linear(embed_dim, embed_dim * 3, bias=True)
        self.attn_drop = nn.Dropout(dropout)
        self.proj = nn.Linear(embed_dim, embed_dim)
        self.proj_drop = nn.Dropout(dropout)
        
    def forward(self, x):
        B, N, C = x.shape
        qkv = self.qkv(x).reshape(B, N, 3, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        q, k, v = qkv[0], qkv[1], qkv[2]
        
        # Use scaled dot product attention (more memory efficient)
        attn = torch.nn.functional.scaled_dot_product_attention(
            q, k, v, dropout_p=self.attn_drop.p if self.training else 0.0
        )
        
        x = attn.transpose(1, 2).reshape(B, N, C)
        x = self.proj(x)
        x = self.proj_drop(x)
        return x


class TransformerBlock(nn.Module):
    """Transformer block with gradient checkpointing option"""
    def __init__(self, embed_dim, num_heads, mlp_ratio=4, dropout=0.1, use_checkpoint=True):
        super().__init__()
        self.use_checkpoint = use_checkpoint
        self.norm1 = nn.LayerNorm(embed_dim)
        self.attn = EfficientMultiHeadAttention(embed_dim, num_heads, dropout)
        self.norm2 = nn.LayerNorm(embed_dim)
        
        mlp_hidden_dim = int(embed_dim * mlp_ratio)
        self.mlp = nn.Sequential(
            nn.Linear(embed_dim, mlp_hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(mlp_hidden_dim, embed_dim),
            nn.Dropout(dropout)
        )
        
    def forward(self, x):
        if self.use_checkpoint and self.training:
            x = x + checkpoint.checkpoint(self._attn_block, x)
            x = x + checkpoint.checkpoint(self._mlp_block, x)
        else:
            x = x + self._attn_block(x)
            x = x + self._mlp_block(x)
        return x
    
    def _attn_block(self, x):
        return self.attn(self.norm1(x))
    
    def _mlp_block(self, x):
        return self.mlp(self.norm2(x))


class EfficientVisionTransformer(nn.Module):
    """Memory-efficient Vision Transformer encoder"""
    def __init__(self, img_size=64, patch_size=2, in_channels=768, embed_dim=768, 
                 depth=12, num_heads=12, mlp_ratio=4, dropout=0.1):
        super().__init__()
        
        # Use larger patches to reduce sequence length
        self.patch_embed = EfficientPatchEmbedding(img_size, patch_size, in_channels, embed_dim)
        self.pos_embed = nn.Parameter(torch.zeros(1, self.patch_embed.n_patches, embed_dim))
        self.pos_drop = nn.Dropout(dropout)
        
        # Use gradient checkpointing for memory efficiency
        self.blocks = nn.ModuleList([
            TransformerBlock(embed_dim, num_heads, mlp_ratio, dropout, use_checkpoint=True)
            for _ in range(depth)
        ])
        
        self.norm = nn.LayerNorm(embed_dim)
        
        # Initialize position embedding
        nn.init.trunc_normal_(self.pos_embed, std=0.02)
        
    def forward(self, x):
        B, C, H, W = x.shape
        x = self.patch_embed(x)
        x = x + self.pos_embed
        x = self.pos_drop(x)
        
        for block in self.blocks:
            x = block(x)
            
        x = self.norm(x)
        
        # Reshape back to spatial
        patch_size = self.patch_embed.patch_size
        h = H // patch_size
        w = W // patch_size
        x = x.transpose(1, 2).reshape(B, -1, h, w)
        
        return x


class ResNetEncoder(nn.Module):
    """Lightweight ResNet encoder for TransUNet"""
    def __init__(self, in_channels=3, use_instance_norm=True):
        super().__init__()
        self.conv1 = nn.Conv2d(in_channels, 64, kernel_size=7, stride=2, padding=3, bias=False)
        self.bn1 = get_norm_layer(64, use_instance_norm)
        self.relu = nn.ReLU(inplace=True)
        self.maxpool = nn.MaxPool2d(kernel_size=3, stride=2, padding=1)
        
        # Simplified ResNet blocks
        self.layer1 = self._make_layer(64, 64, 2, stride=1, use_instance_norm=use_instance_norm)
        self.layer2 = self._make_layer(64, 128, 2, stride=2, use_instance_norm=use_instance_norm)
        self.layer3 = self._make_layer(128, 256, 2, stride=2, use_instance_norm=use_instance_norm)
        self.layer4 = self._make_layer(256, 256, 2, stride=2, use_instance_norm=use_instance_norm)
        
    def _make_layer(self, in_channels, out_channels, blocks, stride, use_instance_norm):
        layers = []
        layers.append(nn.Conv2d(in_channels, out_channels, kernel_size=3, stride=stride, padding=1, bias=False))
        layers.append(get_norm_layer(out_channels, use_instance_norm))
        layers.append(nn.ReLU(inplace=True))
        
        for _ in range(1, blocks):
            layers.append(nn.Conv2d(out_channels, out_channels, kernel_size=3, stride=1, padding=1, bias=False))
            layers.append(get_norm_layer(out_channels, use_instance_norm))
            layers.append(nn.ReLU(inplace=True))
            
        return nn.Sequential(*layers)
        
    def forward(self, x):
        skip_features = []
        
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)
        skip_features.append(x)  # 1/2
        
        x = self.maxpool(x)
        x = self.layer1(x)
        skip_features.append(x)  # 1/4
        
        x = self.layer2(x)
        skip_features.append(x)  # 1/8
        
        x = self.layer3(x)
        skip_features.append(x)  # 1/16
        
        x = self.layer4(x)  # 1/32
        
        return x, skip_features


class DecoderBlock(nn.Module):
    """Decoder block with skip connection"""
    def __init__(self, in_channels, skip_channels, out_channels, use_instance_norm=True):
        super().__init__()
        self.up = nn.ConvTranspose2d(in_channels, in_channels // 2, kernel_size=2, stride=2)
        
        self.conv = nn.Sequential(
            nn.Conv2d(in_channels // 2 + skip_channels, out_channels, kernel_size=3, padding=1),
            get_norm_layer(out_channels, use_instance_norm),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1),
            get_norm_layer(out_channels, use_instance_norm),
            nn.ReLU(inplace=True)
        )
        
    def forward(self, x, skip):
        x = self.up(x)
        x = torch.cat([x, skip], dim=1)
        x = self.conv(x)
        return x


class EfficientTransUNet(nn.Module):
    """Memory-efficient TransUNet for medical image segmentation"""
    
    def __init__(self, img_size=1024, in_channels=3, out_channels=1, 
                 embed_dim=512, depth=8, num_heads=8, mlp_ratio=3,
                 patch_size=2, use_instance_norm=True, pretrained=True):
        super().__init__()
        
        # CNN Encoder
        self.cnn_encoder = ResNetEncoder(in_channels, use_instance_norm)
        
        # CNN to Transformer projection
        self.conv_proj = nn.Conv2d(256, embed_dim, kernel_size=1)
        
        # Efficient Transformer Encoder
        self.transformer = EfficientVisionTransformer(
            img_size=img_size // 16,  # After CNN downsampling
            patch_size=patch_size,  # Use 2x2 patches to reduce sequence length
            in_channels=embed_dim,
            embed_dim=embed_dim,
            depth=depth,  # Fewer layers
            num_heads=num_heads,  # Fewer heads
            mlp_ratio=mlp_ratio  # Smaller MLP
        )
        
        # Transformer to CNN projection
        self.conv_transpose = nn.Sequential(
            nn.Conv2d(embed_dim, 512, kernel_size=3, padding=1),
            get_norm_layer(512, use_instance_norm),
            nn.ReLU(inplace=True)
        )
        
        # Decoder
        self.decoder4 = DecoderBlock(512, 256, 256, use_instance_norm)
        self.decoder3 = DecoderBlock(256, 128, 128, use_instance_norm)
        self.decoder2 = DecoderBlock(128, 64, 64, use_instance_norm)
        self.decoder1 = DecoderBlock(64, 64, 64, use_instance_norm)
        
        # Final convolution
        self.final_conv = nn.Conv2d(64, out_channels, kernel_size=1)
        
        # Initialize weights
        self._init_weights()
        
    def _init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, (nn.BatchNorm2d, nn.InstanceNorm2d)):
                if hasattr(m, 'weight') and m.weight is not None:
                    nn.init.constant_(m.weight, 1)
                if hasattr(m, 'bias') and m.bias is not None:
                    nn.init.constant_(m.bias, 0)
                    
    def forward(self, x):
        # CNN Encoder
        cnn_features, skip_features = self.cnn_encoder(x)
        
        # Project to transformer dimension
        x = self.conv_proj(cnn_features)
        
        # Transformer encoding
        x = self.transformer(x)
        
        # Project back to CNN dimension
        x = self.conv_transpose(x)
        
        # Decoder with skip connections
        x = self.decoder4(x, skip_features[3])
        x = self.decoder3(x, skip_features[2])
        x = self.decoder2(x, skip_features[1])
        x = self.decoder1(x, skip_features[0])
        
        # Final prediction
        x = self.final_conv(x)
        
        return x


# Alias for compatibility
TransUNet = EfficientTransUNet