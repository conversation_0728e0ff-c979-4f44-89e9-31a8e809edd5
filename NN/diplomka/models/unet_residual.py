# unet_residual.py
# UNet with Residual Blocks (without attention mechanisms)

import torch
import torch.nn as nn
import torch.nn.functional as F


# ===========================
# Normalization Helper
# ===========================
def get_norm_layer(num_features, use_instance_norm=True):
    """Get normalization layer (Instance or Batch)"""
    if use_instance_norm:
        return nn.InstanceNorm2d(num_features, affine=True)
    else:
        return nn.BatchNorm2d(num_features)


# ===========================
# Basic Residual Block (without attention)
# ===========================
class ResidualBlock(nn.Module):
    """
    Residual block without attention mechanisms
    Pure residual connection for better gradient flow
    """
    def __init__(self, in_channels, out_channels, use_instance_norm=True, dropout=0.1):
        super(ResidualBlock, self).__init__()
        
        # First convolution path
        self.conv1 = nn.Conv2d(in_channels, out_channels, kernel_size=3, padding=1, bias=False)
        self.norm1 = get_norm_layer(out_channels, use_instance_norm)
        self.relu = nn.ReLU(inplace=True)
        self.dropout1 = nn.Dropout2d(p=dropout * 0.5)
        
        # Second convolution path
        self.conv2 = nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1, bias=False)
        self.norm2 = get_norm_layer(out_channels, use_instance_norm)
        self.dropout2 = nn.Dropout2d(p=dropout)
        
        # Residual connection adjustment
        self.adjust_channels = None
        if in_channels != out_channels:
            self.adjust_channels = nn.Sequential(
                nn.Conv2d(in_channels, out_channels, kernel_size=1, bias=False),
                get_norm_layer(out_channels, use_instance_norm)
            )
    
    def forward(self, x):
        # Store residual
        residual = x
        if self.adjust_channels:
            residual = self.adjust_channels(x)
        
        # Forward path
        out = self.conv1(x)
        out = self.norm1(out)
        out = self.relu(out)
        out = self.dropout1(out)
        
        out = self.conv2(out)
        out = self.norm2(out)
        out = self.dropout2(out)
        
        # Add residual connection
        out += residual
        out = self.relu(out)
        
        return out


# ===========================
# UNet with Residual Blocks
# ===========================
class UNetResidual(nn.Module):
    """
    UNet architecture with residual blocks instead of double convolutions
    Improves gradient flow and training stability
    """
    def __init__(self, in_channels=3, out_channels=1,
                 features=[64, 128, 256, 512, 1024],
                 use_instance_norm=True, dropout_rate=0.15,
                 use_deep_supervision=False):
        super(UNetResidual, self).__init__()
        
        self.use_instance_norm = use_instance_norm
        self.use_deep_supervision = use_deep_supervision
        
        # Initial convolution
        self.init_conv = nn.Sequential(
            nn.Conv2d(in_channels, features[0], kernel_size=7, stride=1, padding=3, bias=False),
            get_norm_layer(features[0], use_instance_norm),
            nn.ReLU(inplace=True),
            nn.Dropout2d(p=dropout_rate * 0.3)
        )
        
        # Encoder path
        self.encoder_blocks = nn.ModuleList()
        self.pools = nn.ModuleList()
        
        for i in range(len(features) - 1):
            self.encoder_blocks.append(
                ResidualBlock(features[i], features[i+1], use_instance_norm, 
                            dropout=dropout_rate if i > 0 else dropout_rate * 0.5)
            )
            self.pools.append(nn.MaxPool2d(kernel_size=2, stride=2))
        
        # Bottleneck with double residual blocks
        self.bottleneck = nn.Sequential(
            ResidualBlock(features[-2], features[-1], use_instance_norm, dropout=dropout_rate * 1.2),
            ResidualBlock(features[-1], features[-1], use_instance_norm, dropout=dropout_rate * 1.2)
        )
        
        # Decoder path
        self.ups = nn.ModuleList()
        self.decoder_blocks = nn.ModuleList()
        
        reversed_features = list(reversed(features))
        for i in range(len(reversed_features) - 1):
            # Upsampling
            self.ups.append(
                nn.ConvTranspose2d(reversed_features[i], reversed_features[i+1],
                                 kernel_size=2, stride=2)
            )
            # Decoder residual block (takes concatenated features)
            self.decoder_blocks.append(
                ResidualBlock(reversed_features[i], reversed_features[i+1],
                            use_instance_norm, dropout=dropout_rate)
            )
        
        # Final output layers
        self.final_conv = nn.Sequential(
            nn.Conv2d(features[0], features[0] // 2, kernel_size=3, padding=1, bias=False),
            get_norm_layer(features[0] // 2, use_instance_norm),
            nn.ReLU(inplace=True),
            nn.Dropout2d(p=dropout_rate * 0.5),
            nn.Conv2d(features[0] // 2, out_channels, kernel_size=1)
        )
        
        # Deep supervision outputs (optional)
        if use_deep_supervision:
            self.deep_outputs = nn.ModuleList([
                nn.Conv2d(features[i], out_channels, kernel_size=1)
                for i in range(len(features) - 1)
            ])
        
        # Initialize weights
        self._init_weights()
    
    def _init_weights(self):
        """Initialize weights using He initialization"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, (nn.BatchNorm2d, nn.InstanceNorm2d)):
                if m.weight is not None:
                    nn.init.constant_(m.weight, 1)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        # Store original input size
        input_size = x.shape[2:]
        
        # Initial convolution
        x = self.init_conv(x)
        
        # Encoder path with skip connections
        skip_connections = [x]
        
        for i, (pool, encoder) in enumerate(zip(self.pools[:-1], self.encoder_blocks[:-1])):
            x = pool(skip_connections[-1])
            x = encoder(x)
            skip_connections.append(x)
        
        # Bottleneck
        x = self.pools[-1](skip_connections[-1])
        x = self.bottleneck(x)
        
        # Reverse skip connections for decoder
        skip_connections = skip_connections[::-1]
        
        # Decoder path
        deep_outputs = []
        for i, (up, decoder) in enumerate(zip(self.ups, self.decoder_blocks)):
            # Upsample
            x = up(x)
            
            # Get skip connection
            skip = skip_connections[i]
            
            # Ensure spatial dimensions match
            if x.shape[2:] != skip.shape[2:]:
                x = F.interpolate(x, size=skip.shape[2:], mode='bilinear', align_corners=False)
            
            # Concatenate skip connection
            x = torch.cat([skip, x], dim=1)
            
            # Apply decoder block
            x = decoder(x)
            
            # Store for deep supervision if enabled
            if self.use_deep_supervision and self.training and i < len(self.deep_outputs):
                deep_idx = len(self.deep_outputs) - 1 - i
                deep_out = self.deep_outputs[deep_idx](x)
                deep_out = F.interpolate(deep_out, size=input_size, mode='bilinear', align_corners=False)
                deep_outputs.append(deep_out)
        
        # Final output
        x = self.final_conv(x)
        
        # Ensure output matches input size
        if x.shape[2:] != input_size:
            x = F.interpolate(x, size=input_size, mode='bilinear', align_corners=False)
        
        if self.use_deep_supervision and self.training:
            return x, deep_outputs
        return x


# Alias for compatibility
ResUNet = UNetResidual