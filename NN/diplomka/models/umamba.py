# U-Mamba: State Space Model based U-Net for Medical Image Segmentation
import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from einops import rearrange, repeat


def get_norm_layer(num_features, use_instance_norm=True):
    """Get normalization layer"""
    if use_instance_norm:
        return nn.InstanceNorm2d(num_features, affine=True)
    else:
        return nn.BatchNorm2d(num_features)


class MambaBlock(nn.Module):
    """
    Mamba block implementation - State Space Model
    Based on the Mamba paper: https://arxiv.org/abs/2312.00752
    """
    def __init__(self, d_model, d_state=8, d_conv=4, expand=1.5, dt_rank="auto", 
                 dt_min=0.001, dt_max=0.1, dt_init="random", dt_scale=1.0, 
                 dt_init_floor=1e-4, dropout=0.0, conv_bias=True, bias=False,
                 use_fast_path=True):
        super().__init__()
        
        self.d_model = d_model
        self.d_state = d_state
        self.d_conv = d_conv
        self.expand = expand
        self.d_inner = int(self.expand * self.d_model)
        self.dt_rank = math.ceil(self.d_model / 16) if dt_rank == "auto" else dt_rank
        self.use_fast_path = use_fast_path
        
        # Input projection
        self.in_proj = nn.Linear(self.d_model, self.d_inner * 2, bias=bias)
        
        # Convolution
        self.conv1d = nn.Conv1d(
            in_channels=self.d_inner,
            out_channels=self.d_inner,
            bias=conv_bias,
            kernel_size=d_conv,
            groups=self.d_inner,
            padding=d_conv - 1,
        )
        
        # SSM parameters
        self.x_proj = nn.Linear(self.d_inner, self.dt_rank + self.d_state * 2, bias=False)
        self.dt_proj = nn.Linear(self.dt_rank, self.d_inner, bias=True)
        
        # Initialize dt projection
        dt_init_std = self.dt_rank**-0.5 * dt_scale
        if dt_init == "constant":
            nn.init.constant_(self.dt_proj.weight, dt_init_std)
        elif dt_init == "random":
            nn.init.uniform_(self.dt_proj.weight, -dt_init_std, dt_init_std)
        else:
            raise NotImplementedError
            
        # Initialize dt bias
        dt = torch.exp(
            torch.rand(self.d_inner) * (math.log(dt_max) - math.log(dt_min))
            + math.log(dt_min)
        ).clamp(min=dt_init_floor)
        inv_dt = dt + torch.log(-torch.expm1(-dt))
        with torch.no_grad():
            self.dt_proj.bias.copy_(inv_dt)
            
        # SSM parameters
        A = repeat(torch.arange(1, self.d_state + 1), 'n -> d n', d=self.d_inner)
        self.A_log = nn.Parameter(torch.log(A))
        self.A_log._no_weight_decay = True
        
        self.D = nn.Parameter(torch.ones(self.d_inner))
        self.D._no_weight_decay = True
        
        # Output projection
        self.out_proj = nn.Linear(self.d_inner, self.d_model, bias=bias)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x):
        """
        x: (B, L, D)
        Returns: same shape as x
        """
        batch, seqlen, dim = x.shape
        
        # Input projection
        xz = self.in_proj(x)
        x, z = xz.chunk(2, dim=-1)
        
        # Convolution
        x = rearrange(x, 'b l d -> b d l')
        x = self.conv1d(x)[:, :, :seqlen]
        x = rearrange(x, 'b d l -> b l d')
        
        # Activation
        x = F.silu(x)
        
        # SSM
        y = self.ssm(x)
        
        # Gating
        y = y * F.silu(z)
        
        # Output projection
        output = self.dropout(self.out_proj(y))
        
        return output
        
    def ssm(self, x):
        """Memory-efficient State Space Model computation"""
        batch, seqlen, dim = x.shape
        A = -torch.exp(self.A_log.float())
        D = self.D.float()
        
        # Compute deltaB, deltaC
        x_dbl = self.x_proj(x)
        delta, B, C = x_dbl.split([self.dt_rank, self.d_state, self.d_state], dim=-1)
        delta = F.softplus(self.dt_proj(delta))
        
        # Process in smaller chunks to avoid large intermediate tensors
        chunk_size = min(256, seqlen)
        y_chunks = []
        
        for i in range(0, seqlen, chunk_size):
            end_idx = min(i + chunk_size, seqlen)
            
            # Discretize only for this chunk
            delta_chunk = delta[:, i:end_idx]
            deltaA_chunk = torch.exp(delta_chunk.unsqueeze(-1) * A)
            deltaB_chunk = delta_chunk.unsqueeze(-1) * B[:, i:end_idx].unsqueeze(-2)
            
            # Process chunk
            y_chunk = self.selective_scan(
                x[:, i:end_idx], 
                deltaA_chunk, 
                deltaB_chunk, 
                C[:, i:end_idx], 
                D
            )
            y_chunks.append(y_chunk)
        
        y = torch.cat(y_chunks, dim=1)
        return y
        
    def selective_scan(self, u, deltaA, deltaB, C, D):
        """Memory-efficient selective scan algorithm for SSM"""
        batch, seqlen, dim = u.shape
        
        # Process in chunks to save memory
        chunk_size = min(64, seqlen)  # Process 64 time steps at once
        
        # Initialize state
        x = torch.zeros(batch, dim, self.d_state, device=u.device, dtype=u.dtype)
        outputs = []
        
        # Process in chunks
        for chunk_start in range(0, seqlen, chunk_size):
            chunk_end = min(chunk_start + chunk_size, seqlen)
            chunk_len = chunk_end - chunk_start
            
            # Preallocate output for this chunk
            chunk_ys = torch.zeros(batch, chunk_len, dim, device=u.device, dtype=u.dtype)
            
            # Process chunk sequentially
            for i in range(chunk_len):
                idx = chunk_start + i
                x = deltaA[:, idx] * x + deltaB[:, idx] * u[:, idx].unsqueeze(-1)
                chunk_ys[:, i] = torch.einsum('bdn,bn->bd', x, C[:, idx])
            
            outputs.append(chunk_ys)
        
        y = torch.cat(outputs, dim=1)
        y = y + u * D
        
        return y


class MambaLayer(nn.Module):
    """Mamba layer with normalization and residual connection"""
    def __init__(self, d_model, d_state=8, d_conv=4, expand=1.5, dropout=0.1):
        super().__init__()
        self.norm = nn.LayerNorm(d_model)
        self.mamba = MambaBlock(
            d_model=d_model,
            d_state=d_state,
            d_conv=d_conv,
            expand=expand,
            dropout=dropout
        )
        
    def forward(self, x):
        # x: (B, C, H, W)
        B, C, H, W = x.shape
        
        # Reshape to sequence
        x_seq = rearrange(x, 'b c h w -> b (h w) c')
        
        # Apply Mamba block with residual
        output = x_seq + self.mamba(self.norm(x_seq))
        
        # Reshape back
        output = rearrange(output, 'b (h w) c -> b c h w', h=H, w=W)
        
        return output


class DoubleConv(nn.Module):
    """Double convolution block"""
    def __init__(self, in_channels, out_channels, use_instance_norm=True):
        super().__init__()
        self.double_conv = nn.Sequential(
            nn.Conv2d(in_channels, out_channels, kernel_size=3, padding=1, bias=False),
            get_norm_layer(out_channels, use_instance_norm),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1, bias=False),
            get_norm_layer(out_channels, use_instance_norm),
            nn.ReLU(inplace=True)
        )
        
    def forward(self, x):
        return self.double_conv(x)


class MambaEncoderBlock(nn.Module):
    """Encoder block with CNN and Mamba"""
    def __init__(self, in_channels, out_channels, use_mamba=True, use_instance_norm=True):
        super().__init__()
        self.conv = DoubleConv(in_channels, out_channels, use_instance_norm)
        self.use_mamba = use_mamba
        
        if use_mamba:
            self.mamba = MambaLayer(out_channels, d_state=8, d_conv=4, expand=1.5)
            
        self.pool = nn.MaxPool2d(2)
        
    def forward(self, x):
        x = self.conv(x)
        
        if self.use_mamba:
            x = self.mamba(x)
            
        skip = x
        x = self.pool(x)
        
        return x, skip


class MambaDecoderBlock(nn.Module):
    """Decoder block with CNN and Mamba"""
    def __init__(self, in_channels, skip_channels, out_channels, use_mamba=True, use_instance_norm=True):
        super().__init__()
        self.up = nn.ConvTranspose2d(in_channels, in_channels // 2, kernel_size=2, stride=2)
        self.conv = DoubleConv(in_channels // 2 + skip_channels, out_channels, use_instance_norm)
        self.use_mamba = use_mamba
        
        if use_mamba:
            self.mamba = MambaLayer(out_channels, d_state=8, d_conv=4, expand=1.5)
            
    def forward(self, x, skip):
        x = self.up(x)
        x = torch.cat([x, skip], dim=1)
        x = self.conv(x)
        
        if self.use_mamba:
            x = self.mamba(x)
            
        return x


class UMamba(nn.Module):
    """U-Mamba: U-Net with Mamba blocks for medical image segmentation"""
    
    def __init__(self, in_channels=3, out_channels=1, features=[64, 128, 256, 512, 1024], 
                 use_instance_norm=True, use_mamba_in_encoder=True, use_mamba_in_decoder=True,
                 use_mamba_in_bottleneck=True):
        super().__init__()
        
        self.encoder_blocks = nn.ModuleList()
        self.decoder_blocks = nn.ModuleList()
        
        # Encoder path
        for i, (in_feat, out_feat) in enumerate(zip([in_channels] + features[:-1], features)):
            # Use Mamba in deeper layers (more global context needed)
            use_mamba = use_mamba_in_encoder and (i >= 2)
            self.encoder_blocks.append(
                MambaEncoderBlock(in_feat, out_feat, use_mamba=use_mamba, use_instance_norm=use_instance_norm)
            )
        
        # Bottleneck with optional Mamba
        self.bottleneck = DoubleConv(features[-1], features[-1], use_instance_norm)
        if use_mamba_in_bottleneck:
            self.bottleneck_mamba = MambaLayer(features[-1], d_state=16, d_conv=4, expand=1.5)
        else:
            self.bottleneck_mamba = None
            
        # Decoder path
        # We need 5 decoder blocks to match the 5 encoder blocks for proper upsampling
        decoder_configs = [
            (1024, 1024, 512),  # in: 1024, skip: 1024, out: 512
            (512, 512, 256),    # in: 512, skip: 512, out: 256
            (256, 256, 128),    # in: 256, skip: 256, out: 128
            (128, 128, 64),     # in: 128, skip: 128, out: 64
            (64, 64, 64),       # in: 64, skip: 64, out: 64 (final upsampling)
        ]
        
        for i, (in_ch, skip_ch, out_ch) in enumerate(decoder_configs):
            # Use Mamba in deeper layers
            use_mamba = use_mamba_in_decoder and (i <= 1)
            self.decoder_blocks.append(
                MambaDecoderBlock(in_ch, skip_ch, out_ch, 
                                use_mamba=use_mamba, use_instance_norm=use_instance_norm)
            )
            
        # Final convolution
        self.final_conv = nn.Conv2d(features[0], out_channels, kernel_size=1)
        
        # Initialize weights
        self._init_weights()
        
    def _init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, (nn.BatchNorm2d, nn.InstanceNorm2d)):
                if m.weight is not None:
                    nn.init.constant_(m.weight, 1)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
                    
    def forward(self, x):
        skip_connections = []
        
        # Encoder - collect all skip connections
        for encoder in self.encoder_blocks:
            x, skip = encoder(x)
            skip_connections.append(skip)
        
        # Bottleneck
        x = self.bottleneck(x)
        if self.bottleneck_mamba is not None:
            x = self.bottleneck_mamba(x)
            
        # Decoder - use all skip connections in reverse order (deep to shallow)
        for decoder, skip in zip(self.decoder_blocks, skip_connections[::-1]):
            x = decoder(x, skip)
            
        # Final output
        x = self.final_conv(x)
        
        return x