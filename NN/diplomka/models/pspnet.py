# PSPNet implementation for semantic segmentation
import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models


def get_norm_layer(num_features, use_instance_norm=True):
    """Get normalization layer"""
    if use_instance_norm:
        return nn.InstanceNorm2d(num_features, affine=True)
    else:
        return nn.BatchNorm2d(num_features)


class PyramidPoolingModule(nn.Module):
    """Pyramid Pooling Module for PSPNet"""
    
    def __init__(self, in_channels, pool_sizes=[1, 2, 3, 6], use_instance_norm=True):
        super(PyramidPoolingModule, self).__init__()
        
        self.pool_sizes = pool_sizes
        out_channels = in_channels // len(pool_sizes)
        
        self.stages = nn.ModuleList()
        for size in pool_sizes:
            # For pool_size=1, skip normalization to avoid spatial size issues
            if size == 1:
                self.stages.append(nn.Sequential(
                    nn.AdaptiveAvgPool2d(output_size=size),
                    nn.Conv2d(in_channels, out_channels, kernel_size=1, bias=False),
                    nn.ReLU(inplace=True)
                ))
            else:
                self.stages.append(nn.Sequential(
                    nn.AdaptiveAvgPool2d(output_size=size),
                    nn.Conv2d(in_channels, out_channels, kernel_size=1, bias=False),
                    get_norm_layer(out_channels, use_instance_norm),
                    nn.ReLU(inplace=True)
                ))
        
        # Bottleneck after concatenation
        self.bottleneck = nn.Sequential(
            nn.Conv2d(in_channels + out_channels * len(pool_sizes), 
                     in_channels // 2, kernel_size=3, padding=1, bias=False),
            get_norm_layer(in_channels // 2, use_instance_norm),
            nn.ReLU(inplace=True),
            nn.Dropout2d(0.1)
        )

    def forward(self, x):
        h, w = x.size(2), x.size(3)
        
        # Apply pyramid pooling
        pyramid_features = [x]
        for stage in self.stages:
            pooled = stage(x)
            upsampled = F.interpolate(pooled, size=(h, w), mode='bilinear', align_corners=True)
            pyramid_features.append(upsampled)
        
        # Concatenate all features
        output = torch.cat(pyramid_features, dim=1)
        output = self.bottleneck(output)
        
        return output


class PSPNet(nn.Module):
    """PSPNet for semantic segmentation"""
    
    def __init__(self, n_class=1, backbone='resnet101', pretrained=True, 
                 pool_sizes=[1, 2, 3, 6], deep_features_size=2048, use_instance_norm=True):
        super(PSPNet, self).__init__()
        self.use_instance_norm = use_instance_norm
        
        self.n_class = n_class
        
        # Load backbone
        if backbone == 'resnet50':
            resnet = models.resnet50(weights='IMAGENET1K_V1' if pretrained else None)
            deep_features_size = 2048
        elif backbone == 'resnet101':
            resnet = models.resnet101(weights='IMAGENET1K_V1' if pretrained else None)
            deep_features_size = 2048
        elif backbone == 'resnet152':
            resnet = models.resnet152(weights='IMAGENET1K_V1' if pretrained else None)
            deep_features_size = 2048
        else:
            raise ValueError(f"Unknown backbone: {backbone}")
        
        # Remove final layers
        self.layer0 = nn.Sequential(
            resnet.conv1,
            resnet.bn1,
            resnet.relu,
            resnet.maxpool
        )
        self.layer1 = resnet.layer1
        self.layer2 = resnet.layer2
        self.layer3 = resnet.layer3
        self.layer4 = resnet.layer4
        
        # Modify stride in layer3 and layer4 for dilation
        for n, m in self.layer3.named_modules():
            if 'conv2' in n:
                m.dilation = (2, 2)
                m.padding = (2, 2)
                m.stride = (1, 1)
            elif 'downsample.0' in n:
                m.stride = (1, 1)
                
        for n, m in self.layer4.named_modules():
            if 'conv2' in n:
                m.dilation = (4, 4)
                m.padding = (4, 4)
                m.stride = (1, 1)
            elif 'downsample.0' in n:
                m.stride = (1, 1)
        
        # Pyramid Pooling Module
        self.ppm = PyramidPoolingModule(deep_features_size, pool_sizes, use_instance_norm)
        
        # Final prediction head
        self.final = nn.Sequential(
            nn.Conv2d(deep_features_size // 2, 512, kernel_size=3, padding=1, bias=False),
            get_norm_layer(512, use_instance_norm),
            nn.ReLU(inplace=True),
            nn.Dropout2d(0.1),
            nn.Conv2d(512, n_class, kernel_size=1)
        )
        
        # Initialize weights
        self._init_weights()

    def _init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

    def forward(self, x):
        input_size = x.size()[2:]
        
        # Backbone forward
        x = self.layer0(x)
        x = self.layer1(x)
        x = self.layer2(x)
        x_aux = self.layer3(x)
        x = self.layer4(x_aux)
        
        # Pyramid pooling
        x = self.ppm(x)
        
        # Final prediction
        x = self.final(x)
        
        # Upsample to original size
        x = F.interpolate(x, size=input_size, mode='bilinear', align_corners=True)
        
        # Return only main output to maintain compatibility with loss function
        return x


class PSPNetWithAux(nn.Module):
    """PSPNet wrapper that handles auxiliary loss during training"""
    
    def __init__(self, n_class=1, backbone='resnet101', pretrained=True, use_instance_norm=True):
        super(PSPNetWithAux, self).__init__()
        self.pspnet = PSPNet(n_class, backbone, pretrained, use_instance_norm=use_instance_norm)
        
    def forward(self, x):
        if self.training:
            main_out, aux_out = self.pspnet(x)
            return main_out  # You can modify this to return both if needed
        else:
            return self.pspnet(x)