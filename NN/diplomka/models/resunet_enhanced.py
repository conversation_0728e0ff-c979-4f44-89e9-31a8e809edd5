# Enhanced ResUNet with modern attention mechanisms
import torch
import torch.nn as nn
import torch.nn.functional as F


# ===========================
# Normalization Helper
# ===========================
def get_norm_layer(num_features, use_instance_norm=True):
    """Get normalization layer (Instance or Batch)"""
    if use_instance_norm:
        return nn.InstanceNorm2d(num_features, affine=True)
    else:
        return nn.BatchNorm2d(num_features)


# ===========================
# CBAM (Convolutional Block Attention Module)
# ===========================
class ChannelAttention(nn.Module):
    """Channel attention module from CBAM"""
    def __init__(self, in_channels, reduction=16):
        super(ChannelAttention, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        
        # Shared MLP
        self.fc = nn.Sequential(
            nn.Conv2d(in_channels, in_channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels // reduction, in_channels, 1, bias=False)
        )
        self.sigmoid = nn.Sigmoid()
        
    def forward(self, x):
        avg_out = self.fc(self.avg_pool(x))
        max_out = self.fc(self.max_pool(x))
        out = avg_out + max_out
        return self.sigmoid(out)


class SpatialAttention(nn.Module):
    """Spatial attention module from CBAM"""
    def __init__(self, kernel_size=7):
        super(SpatialAttention, self).__init__()
        padding = (kernel_size - 1) // 2
        self.conv = nn.Conv2d(2, 1, kernel_size, padding=padding, bias=False)
        self.sigmoid = nn.Sigmoid()
        
    def forward(self, x):
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        out = torch.cat([avg_out, max_out], dim=1)
        out = self.conv(out)
        return self.sigmoid(out)


class CBAM(nn.Module):
    """Convolutional Block Attention Module"""
    def __init__(self, in_channels, reduction=16, kernel_size=7):
        super(CBAM, self).__init__()
        self.channel_attention = ChannelAttention(in_channels, reduction)
        self.spatial_attention = SpatialAttention(kernel_size)
        
    def forward(self, x):
        out = x * self.channel_attention(x)
        out = out * self.spatial_attention(out)
        return out


# ===========================
# ECA-Net (Efficient Channel Attention)
# ===========================
class ECABlock(nn.Module):
    """Efficient Channel Attention block"""
    def __init__(self, channels, k_size=3):
        super(ECABlock, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.conv = nn.Conv1d(1, 1, kernel_size=k_size, padding=(k_size - 1) // 2, bias=False)
        self.sigmoid = nn.Sigmoid()
        
    def forward(self, x):
        # Feature descriptor on the global spatial information
        y = self.avg_pool(x)
        
        # Two different branches of ECA module
        y = self.conv(y.squeeze(-1).transpose(-1, -2)).transpose(-1, -2).unsqueeze(-1)
        
        # Multi-scale information fusion
        y = self.sigmoid(y)
        return x * y.expand_as(x)


# ===========================
# Coordinate Attention
# ===========================
class CoordAttention(nn.Module):
    """Coordinate Attention for mobile networks"""
    def __init__(self, in_channels, reduction=32):
        super(CoordAttention, self).__init__()
        self.pool_h = nn.AdaptiveAvgPool2d((None, 1))
        self.pool_w = nn.AdaptiveAvgPool2d((1, None))
        
        mip = max(8, in_channels // reduction)
        
        self.conv1 = nn.Conv2d(in_channels, mip, kernel_size=1, stride=1, padding=0)
        self.bn1 = nn.BatchNorm2d(mip)
        self.act = nn.ReLU(inplace=True)
        
        self.conv_h = nn.Conv2d(mip, in_channels, kernel_size=1, stride=1, padding=0)
        self.conv_w = nn.Conv2d(mip, in_channels, kernel_size=1, stride=1, padding=0)
        
    def forward(self, x):
        identity = x
        
        n, c, h, w = x.size()
        x_h = self.pool_h(x)
        x_w = self.pool_w(x).permute(0, 1, 3, 2)
        
        y = torch.cat([x_h, x_w], dim=2)
        y = self.conv1(y)
        y = self.bn1(y)
        y = self.act(y)
        
        x_h, x_w = torch.split(y, [h, w], dim=2)
        x_w = x_w.permute(0, 1, 3, 2)
        
        a_h = self.conv_h(x_h).sigmoid()
        a_w = self.conv_w(x_w).sigmoid()
        
        out = identity * a_w * a_h
        return out


# ===========================
# Multi-Scale Attention Block
# ===========================
class MultiScaleAttention(nn.Module):
    """Multi-scale attention combining different attention mechanisms"""
    def __init__(self, in_channels, attention_type='cbam', reduction=16):
        super(MultiScaleAttention, self).__init__()
        self.attention_type = attention_type
        
        if attention_type == 'cbam':
            self.attention = CBAM(in_channels, reduction)
        elif attention_type == 'eca':
            self.attention = ECABlock(in_channels)
        elif attention_type == 'coord':
            self.attention = CoordAttention(in_channels, reduction)
        elif attention_type == 'hybrid':
            # Combine multiple attention mechanisms
            self.channel_att = ChannelAttention(in_channels, reduction)
            self.spatial_att = SpatialAttention()
            self.coord_att = CoordAttention(in_channels, reduction)
            
    def forward(self, x):
        if self.attention_type == 'hybrid':
            # Apply different attention mechanisms and combine
            out = x * self.channel_att(x)
            out = out * self.spatial_att(out)
            out = self.coord_att(out)
            return out
        else:
            return self.attention(x)


# ===========================
# Enhanced Residual Block with Modern Attention
# ===========================
class EnhancedResidualBlock(nn.Module):
    def __init__(self, in_channels, out_channels, dilation=1, attention_type='cbam', 
                 reduction=16, dropout=0.1, use_instance_norm=True):
        super(EnhancedResidualBlock, self).__init__()
        
        # Main convolution path
        self.conv1 = nn.Conv2d(in_channels, out_channels, kernel_size=3, 
                              padding=dilation, dilation=dilation, bias=False)
        self.norm1 = get_norm_layer(out_channels, use_instance_norm)
        self.relu = nn.ReLU(inplace=True)
        self.dropout = nn.Dropout2d(p=dropout)
        
        self.conv2 = nn.Conv2d(out_channels, out_channels, kernel_size=3, 
                              padding=dilation, dilation=dilation, bias=False)
        self.norm2 = get_norm_layer(out_channels, use_instance_norm)
        
        # Residual connection
        self.adjust_channels = nn.Conv2d(in_channels, out_channels, kernel_size=1, 
                                       padding=0, bias=False) if in_channels != out_channels else None
        
        # Attention mechanism
        self.attention = MultiScaleAttention(out_channels, attention_type, reduction)
        
    def forward(self, x):
        residual = x
        if self.adjust_channels:
            residual = self.adjust_channels(x)
            
        out = self.conv1(x)
        out = self.norm1(out)
        out = self.relu(out)
        out = self.dropout(out)
        
        out = self.conv2(out)
        out = self.norm2(out)
        
        out += residual
        out = self.relu(out)
        
        # Apply attention
        out = self.attention(out)
        
        return out


# ===========================
# Enhanced Attention Gate
# ===========================
class EnhancedAttentionGate(nn.Module):
    """Enhanced attention gate with multi-scale features"""
    def __init__(self, F_g, F_l, F_int, use_instance_norm=True):
        super(EnhancedAttentionGate, self).__init__()
        
        self.W_g = nn.Sequential(
            nn.Conv2d(F_g, F_int, kernel_size=1, stride=1, padding=0, bias=False),
            get_norm_layer(F_int, use_instance_norm)
        )
        
        self.W_x = nn.Sequential(
            nn.Conv2d(F_l, F_int, kernel_size=1, stride=1, padding=0, bias=False),
            get_norm_layer(F_int, use_instance_norm)
        )
        
        self.psi = nn.Sequential(
            nn.Conv2d(F_int, 1, kernel_size=1, stride=1, padding=0, bias=True),
            get_norm_layer(1, use_instance_norm),
            nn.Sigmoid()
        )
        
        self.relu = nn.ReLU(inplace=True)
        
        # Additional refinement
        self.refine = nn.Sequential(
            nn.Conv2d(F_l, F_l, kernel_size=3, stride=1, padding=1, bias=False),
            get_norm_layer(F_l, use_instance_norm),
            nn.ReLU(inplace=True)
        )
        
    def forward(self, g, x):
        g1 = self.W_g(g)
        x1 = self.W_x(x)
        psi = self.relu(g1 + x1)
        psi = self.psi(psi)
        
        # Apply attention and refine
        out = x * psi
        out = self.refine(out)
        
        return out


# ===========================
# Enhanced ResUNet with Modern Attention
# ===========================
class EnhancedResUNet(nn.Module):
    def __init__(self, in_channels=3, out_channels=1,
                 features=[16, 32, 64, 128], # Aggressive capacity reduction for generalization
                 attention_type='cbam', use_instance_norm=True, dropout_rate=0.2):
        super(EnhancedResUNet, self).__init__()
        
        self.use_instance_norm = use_instance_norm
        self.attention_type = attention_type
        
        self.ups = nn.ModuleList()
        self.downs = nn.ModuleList()
        self.attentions = nn.ModuleList()
        self.pool = nn.MaxPool2d(kernel_size=2, stride=2)
        
        # Initial convolution
        self.init_conv = nn.Sequential(
            nn.Conv2d(in_channels, features[0], kernel_size=3, padding=1, bias=False),
            get_norm_layer(features[0], use_instance_norm),
            nn.ReLU(inplace=True)
        )
        
        # Down part of ResUNet
        in_features = features[0]
        for feature in features:
            self.downs.append(EnhancedResidualBlock(in_features, feature, 
                                                   attention_type=attention_type,
                                                   use_instance_norm=use_instance_norm))
            in_features = feature
            
        # Bottleneck with multi-scale attention
        self.bottleneck = nn.Sequential(
            EnhancedResidualBlock(features[-1], features[-1] * 2, 
                                attention_type=attention_type,
                                use_instance_norm=use_instance_norm),
            EnhancedResidualBlock(features[-1] * 2, features[-1] * 2, 
                                attention_type=attention_type,
                                use_instance_norm=use_instance_norm)
        )
        
        # Up part of ResUNet
        for feature in reversed(features):
            self.ups.append(nn.ConvTranspose2d(feature * 2, feature, kernel_size=2, stride=2))
            self.ups.append(EnhancedResidualBlock(feature * 2, feature, 
                                                 attention_type=attention_type,
                                                 use_instance_norm=use_instance_norm))
            self.attentions.append(EnhancedAttentionGate(F_g=feature, F_l=feature, 
                                                        F_int=feature // 2, 
                                                        use_instance_norm=use_instance_norm))
            
        # Final layers
        self.final_conv = nn.Sequential(
            nn.Conv2d(features[0], features[0] // 2, kernel_size=3, padding=1, bias=False),
            get_norm_layer(features[0] // 2, use_instance_norm),
            nn.ReLU(inplace=True),
            nn.Conv2d(features[0] // 2, out_channels, kernel_size=1)
        )
        
    def forward(self, x):
        x = self.init_conv(x)
        
        skip_connections = []
        
        # Encoder
        for down in self.downs:
            x = down(x)
            skip_connections.append(x)
            x = self.pool(x)
            
        # Bottleneck
        x = self.bottleneck(x)
        skip_connections = skip_connections[::-1]
        
        # Decoder
        for idx in range(0, len(self.ups), 2):
            x = self.ups[idx](x)
            skip_connection = skip_connections[idx // 2]
            
            # Resize if needed
            if x.shape != skip_connection.shape:
                x = TF.resize(x, size=skip_connection.shape[2:])
                
            # Apply enhanced attention gate
            attention = self.attentions[idx // 2](g=x, x=skip_connection)
            concat_skip = torch.cat((attention, x), dim=1)
            x = self.ups[idx + 1](concat_skip)
            
        # Final output
        x = self.final_conv(x)
        return x


# Backward compatibility
ResUNet = EnhancedResUNet