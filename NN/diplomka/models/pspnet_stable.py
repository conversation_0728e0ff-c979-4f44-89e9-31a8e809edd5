# Stabilized PSPNet implementation for semantic segmentation
import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models


def get_norm_layer(num_features, use_instance_norm=True):
    """Get normalization layer"""
    if use_instance_norm:
        return nn.InstanceNorm2d(num_features, affine=True)
    else:
        return nn.BatchNorm2d(num_features)


class StabilizedPyramidPoolingModule(nn.Module):
    """Stabilized Pyramid Pooling Module for PSPNet"""
    
    def __init__(self, in_channels, pool_sizes=[1, 2, 3, 6], use_instance_norm=True):
        super(StabilizedPyramidPoolingModule, self).__init__()
        
        self.pool_sizes = pool_sizes
        out_channels = max(in_channels // (len(pool_sizes) * 2), 128)  # Ensure minimum channels
        
        self.stages = nn.ModuleList()
        for size in pool_sizes:
            # Special handling for 1x1 pooling - skip normalization to avoid InstanceNorm error
            if size == 1:
                self.stages.append(nn.Sequential(
                    nn.AdaptiveAvgPool2d(output_size=size),
                    nn.Conv2d(in_channels, out_channels, kernel_size=1, bias=True),
                    nn.ReLU(inplace=True)
                ))
            else:
                self.stages.append(nn.Sequential(
                    nn.AdaptiveAvgPool2d(output_size=size),
                    nn.Conv2d(in_channels, out_channels, kernel_size=1, bias=True),
                    get_norm_layer(out_channels, use_instance_norm),
                    nn.ReLU(inplace=True)
                ))
        
        # Bottleneck with residual connection preparation
        bottleneck_channels = in_channels // 2
        self.bottleneck = nn.Sequential(
            nn.Conv2d(in_channels + out_channels * len(pool_sizes), 
                     bottleneck_channels, kernel_size=3, padding=1, bias=False),
            get_norm_layer(bottleneck_channels, use_instance_norm),
            nn.ReLU(inplace=True),
            nn.Dropout2d(0.02)  # Very light dropout
        )
        
        # 1x1 conv to match dimensions for residual
        self.downsample = nn.Conv2d(in_channels, bottleneck_channels, kernel_size=1, bias=False)

    def forward(self, x):
        h, w = x.size(2), x.size(3)
        
        # Store for residual
        identity = self.downsample(x)
        
        # Apply pyramid pooling
        pyramid_features = [x]
        for stage in self.stages:
            pooled = stage(x)
            # Use nearest neighbor for more stable upsampling
            upsampled = F.interpolate(pooled, size=(h, w), mode='nearest')
            pyramid_features.append(upsampled)
        
        # Concatenate all features
        output = torch.cat(pyramid_features, dim=1)
        output = self.bottleneck(output)
        
        # Add residual connection
        output = output + identity
        
        return output


class StabilizedPSPNet(nn.Module):
    """Stabilized PSPNet for semantic segmentation"""
    
    def __init__(self, n_class=1, backbone='resnet50', pretrained=True, 
                 pool_sizes=[1, 2, 3, 6], use_instance_norm=True):
        super(StabilizedPSPNet, self).__init__()
        self.use_instance_norm = use_instance_norm
        self.n_class = n_class
        
        # Load backbone (default to ResNet50 for stability)
        if backbone == 'resnet50':
            resnet = models.resnet50(weights='IMAGENET1K_V1' if pretrained else None)
            deep_features_size = 2048
        elif backbone == 'resnet101':
            resnet = models.resnet101(weights='IMAGENET1K_V1' if pretrained else None)
            deep_features_size = 2048
        else:
            raise ValueError(f"Unknown backbone: {backbone}")
        
        # Freeze early layers for stability
        self.layer0 = nn.Sequential(
            resnet.conv1,
            resnet.bn1,
            resnet.relu,
            resnet.maxpool
        )
        # Optionally freeze early layers
        for param in self.layer0.parameters():
            param.requires_grad = True  # Keep trainable but could freeze
            
        self.layer1 = resnet.layer1
        self.layer2 = resnet.layer2
        self.layer3 = resnet.layer3
        self.layer4 = resnet.layer4
        
        # Less aggressive dilation for stability
        for n, m in self.layer3.named_modules():
            if 'conv2' in n and hasattr(m, 'dilation'):
                m.dilation = (2, 2)
                m.padding = (2, 2)
                m.stride = (1, 1)
            elif 'downsample.0' in n:
                m.stride = (1, 1)
                
        for n, m in self.layer4.named_modules():
            if 'conv2' in n and hasattr(m, 'dilation'):
                m.dilation = (2, 2)  # Same as layer3 for stability
                m.padding = (2, 2)
                m.stride = (1, 1)
            elif 'downsample.0' in n:
                m.stride = (1, 1)
        
        # Stabilized Pyramid Pooling Module
        self.ppm = StabilizedPyramidPoolingModule(deep_features_size, pool_sizes, use_instance_norm)
        
        # Smoother final prediction head
        self.final = nn.Sequential(
            nn.Conv2d(deep_features_size // 2, 512, kernel_size=3, padding=1, bias=False),
            get_norm_layer(512, use_instance_norm),
            nn.ReLU(inplace=True),
            nn.Dropout2d(0.02),
            
            nn.Conv2d(512, 256, kernel_size=3, padding=1, bias=False),
            get_norm_layer(256, use_instance_norm),
            nn.ReLU(inplace=True),
            
            nn.Conv2d(256, 128, kernel_size=3, padding=1, bias=False),
            get_norm_layer(128, use_instance_norm),
            nn.ReLU(inplace=True),
            
            nn.Conv2d(128, n_class, kernel_size=1, bias=True)  # Add bias for final layer
        )
        
        # Initialize weights carefully
        self._init_weights()

    def _init_weights(self):
        for m in self.ppm.modules():
            if isinstance(m, nn.Conv2d):
                # Xavier init for PPM
                nn.init.xavier_uniform_(m.weight, gain=1.0)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
                    
        for m in self.final.modules():
            if isinstance(m, nn.Conv2d):
                if m.out_channels == self.n_class:
                    # Small initialization for final layer
                    nn.init.normal_(m.weight, mean=0.0, std=0.01)
                    if m.bias is not None:
                        nn.init.constant_(m.bias, 0)
                else:
                    # Xavier for other layers
                    nn.init.xavier_uniform_(m.weight, gain=1.0)
                    if m.bias is not None:
                        nn.init.constant_(m.bias, 0)
            elif isinstance(m, (nn.BatchNorm2d, nn.InstanceNorm2d)):
                if m.weight is not None:
                    nn.init.constant_(m.weight, 1)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)

    def forward(self, x):
        input_size = x.size()[2:]
        
        # Backbone forward with gradient checkpointing for memory
        x = self.layer0(x)
        x = self.layer1(x)
        x = self.layer2(x)
        x = self.layer3(x)
        x = self.layer4(x)
        
        # Pyramid pooling
        x = self.ppm(x)
        
        # Final prediction
        x = self.final(x)
        
        # Stable upsampling
        x = F.interpolate(x, size=input_size, mode='bilinear', align_corners=False)
        
        return x


# Alias for compatibility
PSPNet = StabilizedPSPNet