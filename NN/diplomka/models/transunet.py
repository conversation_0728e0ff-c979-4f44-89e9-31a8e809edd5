# TransUNet implementation for medical image segmentation
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from einops import rearrange
from einops.layers.torch import Rearrange
import math


class InstanceNorm2d(nn.Module):
    """Instance Normalization wrapper that can replace BatchNorm"""
    def __init__(self, num_features, eps=1e-5, momentum=0.1, affine=True):
        super().__init__()
        self.norm = nn.InstanceNorm2d(num_features, eps=eps, affine=affine)
        
    def forward(self, x):
        return self.norm(x)


def get_norm_layer(num_features, use_instance_norm=True):
    """Get normalization layer"""
    if use_instance_norm:
        return InstanceNorm2d(num_features)
    else:
        return nn.BatchNorm2d(num_features)


class PatchEmbedding(nn.Module):
    """Image to Patch Embedding"""
    def __init__(self, img_size=1024, patch_size=16, in_channels=3, embed_dim=768):
        super().__init__()
        self.img_size = img_size
        self.patch_size = patch_size
        self.n_patches = (img_size // patch_size) ** 2
        
        # When patch_size=1, we just need to rearrange dimensions
        if patch_size == 1:
            self.proj = nn.Sequential(
                Rearrange('b c h w -> b (h w) c')
            )
        else:
            self.proj = nn.Sequential(
                nn.Conv2d(in_channels, embed_dim, kernel_size=patch_size, stride=patch_size),
                Rearrange('b c h w -> b (h w) c')
            )
        
    def forward(self, x):
        x = self.proj(x)
        return x


class MultiHeadSelfAttention(nn.Module):
    """Multi-Head Self Attention module"""
    def __init__(self, embed_dim, num_heads=12, dropout=0.1):
        super().__init__()
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads
        self.scale = self.head_dim ** -0.5
        
        self.qkv = nn.Linear(embed_dim, embed_dim * 3)
        self.attn_drop = nn.Dropout(dropout)
        self.proj = nn.Linear(embed_dim, embed_dim)
        self.proj_drop = nn.Dropout(dropout)
        
    def forward(self, x):
        B, N, C = x.shape
        qkv = self.qkv(x).reshape(B, N, 3, self.num_heads, self.head_dim).permute(2, 0, 3, 1, 4)
        q, k, v = qkv[0], qkv[1], qkv[2]
        
        attn = (q @ k.transpose(-2, -1)) * self.scale
        attn = attn.softmax(dim=-1)
        attn = self.attn_drop(attn)
        
        x = (attn @ v).transpose(1, 2).reshape(B, N, C)
        x = self.proj(x)
        x = self.proj_drop(x)
        return x


class TransformerBlock(nn.Module):
    """Transformer block with attention and MLP"""
    def __init__(self, embed_dim, num_heads=12, mlp_ratio=4, dropout=0.1):
        super().__init__()
        self.norm1 = nn.LayerNorm(embed_dim)
        self.attn = MultiHeadSelfAttention(embed_dim, num_heads, dropout)
        self.norm2 = nn.LayerNorm(embed_dim)
        
        mlp_hidden_dim = int(embed_dim * mlp_ratio)
        self.mlp = nn.Sequential(
            nn.Linear(embed_dim, mlp_hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(mlp_hidden_dim, embed_dim),
            nn.Dropout(dropout)
        )
        
    def forward(self, x):
        x = x + self.attn(self.norm1(x))
        x = x + self.mlp(self.norm2(x))
        return x


class VisionTransformer(nn.Module):
    """Vision Transformer encoder"""
    def __init__(self, img_size=1024, patch_size=16, in_channels=768, embed_dim=768, 
                 depth=12, num_heads=12, mlp_ratio=4, dropout=0.1):
        super().__init__()
        self.patch_embed = PatchEmbedding(img_size, patch_size, in_channels, embed_dim)
        self.pos_embed = nn.Parameter(torch.zeros(1, self.patch_embed.n_patches, embed_dim))
        self.pos_drop = nn.Dropout(dropout)
        
        self.blocks = nn.ModuleList([
            TransformerBlock(embed_dim, num_heads, mlp_ratio, dropout)
            for _ in range(depth)
        ])
        
        self.norm = nn.LayerNorm(embed_dim)
        
        # Initialize position embedding
        nn.init.trunc_normal_(self.pos_embed, std=0.02)
        
    def forward(self, x):
        x = self.patch_embed(x)
        x = x + self.pos_embed
        x = self.pos_drop(x)
        
        for block in self.blocks:
            x = block(x)
            
        x = self.norm(x)
        return x


class ResNetBlock(nn.Module):
    """ResNet block with instance/batch norm option"""
    def __init__(self, in_channels, out_channels, stride=1, use_instance_norm=True):
        super().__init__()
        self.conv1 = nn.Conv2d(in_channels, out_channels, kernel_size=3, 
                              stride=stride, padding=1, bias=False)
        self.norm1 = get_norm_layer(out_channels, use_instance_norm)
        self.relu = nn.ReLU(inplace=True)
        self.conv2 = nn.Conv2d(out_channels, out_channels, kernel_size=3, 
                              stride=1, padding=1, bias=False)
        self.norm2 = get_norm_layer(out_channels, use_instance_norm)
        
        self.downsample = None
        if stride != 1 or in_channels != out_channels:
            self.downsample = nn.Sequential(
                nn.Conv2d(in_channels, out_channels, kernel_size=1, stride=stride, bias=False),
                get_norm_layer(out_channels, use_instance_norm)
            )
            
    def forward(self, x):
        identity = x
        
        out = self.conv1(x)
        out = self.norm1(out)
        out = self.relu(out)
        
        out = self.conv2(out)
        out = self.norm2(out)
        
        if self.downsample is not None:
            identity = self.downsample(x)
            
        out += identity
        out = self.relu(out)
        
        return out


class ResNetEncoder(nn.Module):
    """ResNet encoder for hybrid CNN-Transformer"""
    def __init__(self, in_channels=3, use_instance_norm=True):
        super().__init__()
        # Initial convolution
        self.conv1 = nn.Conv2d(in_channels, 64, kernel_size=7, stride=2, padding=3, bias=False)
        self.norm1 = get_norm_layer(64, use_instance_norm)
        self.relu = nn.ReLU(inplace=True)
        self.maxpool = nn.MaxPool2d(kernel_size=3, stride=2, padding=1)
        
        # ResNet blocks
        self.layer1 = self._make_layer(64, 64, 3, stride=1, use_instance_norm=use_instance_norm)
        self.layer2 = self._make_layer(64, 128, 4, stride=2, use_instance_norm=use_instance_norm)
        self.layer3 = self._make_layer(128, 256, 6, stride=2, use_instance_norm=use_instance_norm)
        
    def _make_layer(self, in_channels, out_channels, blocks, stride=1, use_instance_norm=True):
        layers = []
        layers.append(ResNetBlock(in_channels, out_channels, stride, use_instance_norm))
        for _ in range(1, blocks):
            layers.append(ResNetBlock(out_channels, out_channels, 1, use_instance_norm))
        return nn.Sequential(*layers)
        
    def forward(self, x):
        # Keep intermediate features for skip connections
        features = []
        
        x = self.conv1(x)
        x = self.norm1(x)
        x = self.relu(x)
        features.append(x)  # 1/2
        
        x = self.maxpool(x)
        x = self.layer1(x)
        features.append(x)  # 1/4
        
        x = self.layer2(x)
        features.append(x)  # 1/8
        
        x = self.layer3(x)
        features.append(x)  # 1/16
        
        return x, features


class DecoderBlock(nn.Module):
    """Decoder block with skip connection"""
    def __init__(self, in_channels, skip_channels, out_channels, use_instance_norm=True):
        super().__init__()
        self.up = nn.ConvTranspose2d(in_channels, in_channels // 2, kernel_size=2, stride=2)
        
        self.conv = nn.Sequential(
            nn.Conv2d(in_channels // 2 + skip_channels, out_channels, kernel_size=3, padding=1),
            get_norm_layer(out_channels, use_instance_norm),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1),
            get_norm_layer(out_channels, use_instance_norm),
            nn.ReLU(inplace=True)
        )
        
    def forward(self, x, skip):
        x = self.up(x)
        x = torch.cat([x, skip], dim=1)
        x = self.conv(x)
        return x


class TransUNet(nn.Module):
    """TransUNet: CNN-Transformer hybrid for medical image segmentation"""
    
    def __init__(self, img_size=1024, in_channels=3, out_channels=1, 
                 embed_dim=768, depth=12, num_heads=12, mlp_ratio=4,
                 patch_size=16, use_instance_norm=True, pretrained=True):
        super().__init__()
        
        self.img_size = img_size
        self.patch_size = patch_size
        self.n_patches = (img_size // patch_size // 16) ** 2  # After CNN encoding
        
        # CNN Encoder
        self.cnn_encoder = ResNetEncoder(in_channels, use_instance_norm)
        
        # CNN to Transformer projection
        self.conv_proj = nn.Conv2d(256, embed_dim, kernel_size=1)
        
        # Transformer Encoder
        self.transformer = VisionTransformer(
            img_size=img_size // 16,  # After CNN downsampling
            patch_size=1,  # Already in patches from CNN
            in_channels=embed_dim,
            embed_dim=embed_dim,
            depth=depth,
            num_heads=num_heads,
            mlp_ratio=mlp_ratio
        )
        
        # Transformer to CNN projection
        self.conv_transpose = nn.Sequential(
            nn.Conv2d(embed_dim, 512, kernel_size=3, padding=1),
            get_norm_layer(512, use_instance_norm),
            nn.ReLU(inplace=True)
        )
        
        # Decoder
        self.decoder4 = DecoderBlock(512, 256, 256, use_instance_norm)
        self.decoder3 = DecoderBlock(256, 128, 128, use_instance_norm)
        self.decoder2 = DecoderBlock(128, 64, 64, use_instance_norm)
        self.decoder1 = DecoderBlock(64, 64, 64, use_instance_norm)
        
        # Final convolution
        self.final_conv = nn.Conv2d(64, out_channels, kernel_size=1)
        
        # Initialize weights
        self._init_weights()
        
    def _init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, (nn.BatchNorm2d, nn.InstanceNorm2d)):
                if hasattr(m, 'weight') and m.weight is not None:
                    nn.init.constant_(m.weight, 1)
                if hasattr(m, 'bias') and m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.trunc_normal_(m.weight, std=0.02)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
                    
    def forward(self, x):
        # CNN encoding
        cnn_features, skip_features = self.cnn_encoder(x)
        
        # Project to transformer dimension
        x = self.conv_proj(cnn_features)
        
        # Reshape for transformer
        B, C, H, W = x.shape
        
        # Transformer encoding - expects 4D input (B, C, H, W)
        x = self.transformer(x)
        
        # Reshape back to spatial - transformer returns (B, num_patches, embed_dim)
        # Need to reshape to (B, embed_dim, H, W)
        x = x.transpose(1, 2).reshape(B, -1, H, W)
        
        # Project back to CNN dimension
        x = self.conv_transpose(x)
        
        # Decoder with skip connections
        x = self.decoder4(x, skip_features[3])
        x = self.decoder3(x, skip_features[2])
        x = self.decoder2(x, skip_features[1])
        x = self.decoder1(x, skip_features[0])
        
        # Final prediction
        x = self.final_conv(x)
        
        # Upsample to original size
        x = F.interpolate(x, scale_factor=2, mode='bilinear', align_corners=True)
        
        return x