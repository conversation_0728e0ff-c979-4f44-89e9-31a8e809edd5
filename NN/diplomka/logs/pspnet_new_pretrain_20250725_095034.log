============================================================
COMPLETE DATASET SUMMARY
============================================================
Train samples: 28,778
Val samples:   2,539
Test samples:  653
Total samples: 31,970
============================================================

TRAIN Dataset Summary:
  Total images found: 28778
  Valid image-mask pairs: 28778
  Images without masks: 0
  Final train dataset size: 28778

VAL Dataset Summary:
  Total images found: 2539
  Valid image-mask pairs: 2539
  Images without masks: 0
  Final val dataset size: 2539

TEST Dataset Summary:
  Total images found: 653
  Valid image-mask pairs: 653
  Images without masks: 0
  Final test dataset size: 653

============================================================
COMPLETE DATASET SUMMARY
============================================================
Dataset path: /data/prusek/training_big
Image size: 1024x1024
------------------------------------------------------------
Train samples: 28,778
Val samples:   2,539
Test samples:  653
------------------------------------------------------------
Total samples: 31,970
============================================================


Epoch 0 Training:   0%|          | 0/2398 [00:00<?, ?it/s]
TRAIN Dataset Summary:
  Total images found: 28778
  Valid image-mask pairs: 28778
  Images without masks: 0
  Final train dataset size: 28778

VAL Dataset Summary:
  Total images found: 2539
  Valid image-mask pairs: 2539
  Images without masks: 0
  Final val dataset size: 2539
terminate called without an active exception
terminate called without an active exception
terminate called without an active exception
terminate called without an active exception
terminate called without an active exception
terminate called without an active exception
terminate called without an active exception
Exception ignored in: <function _MultiProcessingDataLoaderIter.__del__ at 0x7f6a62d91040>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/utils/data/dataloader.py", line 1663, in __del__
    self._shutdown_workers()
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/utils/data/dataloader.py", line 1627, in _shutdown_workers
    w.join(timeout=_utils.MP_STATUS_CHECK_INTERVAL)
  File "/usr/lib64/python3.9/multiprocessing/process.py", line 149, in join
    res = self._popen.wait(timeout)
  File "/usr/lib64/python3.9/multiprocessing/popen_fork.py", line 40, in wait
    if not wait([self.sentinel], timeout):
  File "/usr/lib64/python3.9/multiprocessing/connection.py", line 935, in wait
    ready = selector.select(timeout)
  File "/usr/lib64/python3.9/selectors.py", line 416, in select
    fd_event_list = self._selector.poll(timeout)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/utils/data/_utils/signal_handling.py", line 73, in handler
    _error_if_any_worker_fails()
RuntimeError: DataLoader worker (pid 2410653) is killed by signal: Aborted. 

Epoch 0 Training:   0%|          | 0/2398 [00:25<?, ?it/s]
[rank0]:[W725 09:51:10.215905045 ProcessGroupNCCL.cpp:1479] Warning: WARNING: destroy_process_group() was not called before program exit, which can leak resources. For more info, please see https://pytorch.org/docs/stable/distributed.html#shutdown (function operator())
W0725 09:51:11.095385 2409228 torch/multiprocessing/spawn.py:169] Terminating process 2409387 via signal SIGTERM
Traceback (most recent call last):
  File "/home/<USER>/SpheroSeg/NN/diplomka/CNN_main_spheroid.py", line 1421, in <module>
    main()
  File "/home/<USER>/SpheroSeg/NN/diplomka/CNN_main_spheroid.py", line 1416, in main
    mp.spawn(train, args=(world_size, args), nprocs=world_size, join=True)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 340, in spawn
    return start_processes(fn, args, nprocs, join, daemon, start_method="spawn")
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 296, in start_processes
    while not context.join():
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 215, in join
    raise ProcessRaisedException(msg, error_index, failed_process.pid)
torch.multiprocessing.spawn.ProcessRaisedException: 

-- Process 0 terminated with the following error:
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 90, in _wrap
    fn(i, *args)
  File "/home/<USER>/SpheroSeg/NN/diplomka/CNN_main_spheroid.py", line 1251, in train
    train_loss, train_metrics, loss_components = train_epoch(
  File "/home/<USER>/SpheroSeg/NN/diplomka/CNN_main_spheroid.py", line 476, in train_epoch
    outputs, loss, components = handle_model_output(model, images, criterion, masks, args.aux_weight)
NameError: name 'args' is not defined

