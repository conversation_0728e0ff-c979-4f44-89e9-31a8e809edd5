# Requirements for spheroid segmentation project

# Core dependencies
torch>=2.0.0
torchvision>=0.15.0
numpy>=1.21.0
opencv-python>=4.5.0
albumentations>=1.3.0
tqdm>=4.65.0
matplotlib>=3.5.0
seaborn>=0.12.0
pandas>=1.5.0
scikit-learn>=1.2.0
Pillow>=9.0.0

# For distributed training (included in torch)

# For logging and monitoring  
tensorboard>=2.11.0
tensorboardX>=2.5.0

# For data processing
scipy>=1.9.0
scikit-image>=0.19.0

# Optional for semi-supervised learning
# torch-semi-supervised
# faiss-gpu  # for nearest neighbor search

# Optional for experiment tracking
# wandb
# mlflow

# For TransUNet
einops>=0.6.0

# For model comparison
prettytable>=3.5.0

# For model export
# onnx>=1.13.0
# onnxruntime>=1.14.0