#!/bin/bash

# Skript pro spuštění evaluace všech modelů
# Použití: ./run_evaluation.sh <cesta_k_test_datasetu>

if [ $# -eq 0 ]; then
    echo "Použití: $0 <cesta_k_test_datasetu>"
    echo "Příklad: $0 /path/to/test/dataset"
    echo ""
    echo "Test dataset musí obsahovat slo<PERSON>ky:"
    echo "  - images/    (testova<PERSON><PERSON> obr<PERSON>)"
    echo "  - masks/     (odpovídající masky)"
    exit 1
fi

DATASET_PATH="$1"
MODELS_PATH="$HOME/Desktop/SpheroSeg_trained"
OUTPUT_DIR="evaluation_results_$(date +%Y%m%d_%H%M%S)"

echo "Spouštím evaluaci modelů..."
echo "Dataset: $DATASET_PATH"
echo "Modely: $MODELS_PATH"
echo "Výstup: $OUTPUT_DIR"
echo ""

# Zkontroluj, zda existují potřebn<PERSON> s<PERSON>
if [ ! -d "$DATASET_PATH/images" ]; then
    echo "Chyba: Složka $DATASET_PATH/images neexistuje!"
    echo "Zkontroluj strukturu datasetu. Měl by obsahovat:"
    echo "  - images/    (testovací obrázky)"
    echo "  - masks/     (odpovídající masky)"
    echo ""
    echo "Aktuální obsah $DATASET_PATH:"
    ls -la "$DATASET_PATH" 2>/dev/null || echo "Dataset path neexistuje!"
    exit 1
fi

if [ ! -d "$DATASET_PATH/masks" ]; then
    echo "Chyba: Složka $DATASET_PATH/masks neexistuje!"
    echo "Zkontroluj strukturu datasetu. Měl by obsahovat:"
    echo "  - images/    (testovací obrázky)"
    echo "  - masks/     (odpovídající masky)"
    exit 1
fi

if [ ! -d "$MODELS_PATH" ]; then
    echo "Chyba: Složka s modely $MODELS_PATH neexistuje!"
    echo "Nejprve spusť ./copy_finetuned_models.sh"
    exit 1
fi

# Spusť evaluaci
python3 evaluate_all_models.py \
    --dataset_path "$DATASET_PATH" \
    --models_path "$MODELS_PATH" \
    --output_dir "$OUTPUT_DIR" \
    --batch_size 1 \
    --find_threshold

echo ""
echo "Evaluace dokončena!"
echo "Výsledky jsou v složce: $OUTPUT_DIR"
echo ""
echo "Soubory s výsledky:"
echo "  - detailed_results.json       # Detailní výsledky"
echo "  - model_comparison.csv        # Porovnání modelů"
echo "  - metrics_comparison.png      # Graf metrik"
echo "  - inference_time_comparison.png # Graf časů"
echo "  - radar_chart.png            # Radar chart metrik"