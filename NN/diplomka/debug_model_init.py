"""Debug model initialization"""

# Let's trace through the model initialization
features = [64, 128, 256, 512, 1024]
in_channels = 3
out_channels = 1

print(f"Creating UMamba with:")
print(f"  in_channels: {in_channels}")
print(f"  out_channels: {out_channels}")
print(f"  features: {features}")

# Check what final_conv should be
print(f"\nFinal conv should be: Conv2d({features[0]}, {out_channels}, kernel_size=1)")
print(f"That is: Conv2d(64, 1, kernel_size=1)")

# Let's check the actual model
from models.umamba import UMamba
model = UMamba(in_channels=in_channels, out_channels=out_channels, features=features)

print(f"\nActual final conv: {model.final_conv}")
if model.final_conv.out_channels != out_channels:
    print(f"ERROR: Expected {out_channels} output channels, got {model.final_conv.out_channels}")