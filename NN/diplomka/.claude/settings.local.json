{"permissions": {"allow": ["<PERSON><PERSON>(python:*)", "Bash(ls:*)", "Bash(find:*)", "Bash(rm:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(grep:*)", "Bash(pip install:*)", "Bash(rsync:*)", "Bash(./auto_sync.exp:*)", "<PERSON><PERSON>(sed:*)", "Bash(for:*)", "Bash(do sed -i '' 's|/Volumes/T7/Datasets/sféroidy/all/training_big|/data/prusek/training_big|g' \"$file\")", "Bash(done)", "Bash(do sed -i '' 's/--batch_size 16/--batch_size 4/g' \"$file\")", "Bash(expect:*)", "Bash(sshpass:*)", "Bash(brew install:*)"], "deny": []}}