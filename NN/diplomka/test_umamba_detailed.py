"""Detailed test of U-Mamba model"""

import torch
from models.umamba import UMamba

# Create model
model = UMamba(in_channels=3, out_channels=1, features=[64, 128, 256, 512, 1024])

# Test input
x = torch.randn(1, 3, 256, 256)
print(f"Input shape: {x.shape}")

# Track shapes through the model
def forward_with_shapes(self, x):
    skip_connections = []
    print("\n=== ENCODER ===")
    
    # Encoder - collect all skip connections
    for i, encoder in enumerate(self.encoder_blocks):
        x, skip = encoder(x)
        skip_connections.append(skip)
        print(f"Encoder {i}: output {x.shape}, skip {skip.shape}")
    
    # Bottleneck
    print("\n=== BOTTLENECK ===")
    print(f"Before bottleneck: {x.shape}")
    x = self.bottleneck(x)
    if self.bottleneck_mamba is not None:
        x = self.bottleneck_mamba(x)
    print(f"After bottleneck: {x.shape}")
        
    # Decoder
    print("\n=== DECODER ===")
    decoder_skips = skip_connections[1:][::-1]
    print(f"Skip connections for decoder: {[s.shape for s in decoder_skips]}")
    
    for i, (decoder, skip) in enumerate(zip(self.decoder_blocks, decoder_skips)):
        print(f"\nDecoder {i}:")
        print(f"  Before: {x.shape}")
        print(f"  Skip: {skip.shape}")
        x = decoder(x, skip)
        print(f"  After: {x.shape}")
        
    # Final output
    print("\n=== FINAL CONV ===")
    print(f"Before final conv: {x.shape}")
    x = self.final_conv(x)
    print(f"After final conv: {x.shape}")
    
    return x

# Monkey patch the forward method
original_forward = model.forward
model.forward = lambda x: forward_with_shapes(model, x)

# Run the model
output = model(x)
print(f"\nFinal output shape: {output.shape}")
print(f"Expected shape: torch.Size([1, 1, 256, 256])")

if output.shape[2:] != x.shape[2:]:
    print("\nERROR: Output spatial dimensions don't match input!")