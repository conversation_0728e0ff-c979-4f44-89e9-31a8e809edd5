"""Comprehensive test of U-Mamba model"""

import torch
from models.umamba import UMamba

print("=== U-MAMBA COMPREHENSIVE TEST ===\n")

# Test 1: Basic functionality
print("Test 1: Basic forward pass")
model = UMamba(in_channels=3, out_channels=1, features=[64, 128, 256, 512, 1024])
x = torch.randn(2, 3, 256, 256)
output = model(x)
print(f"✓ Input shape: {x.shape}")
print(f"✓ Output shape: {output.shape}")
assert output.shape == (2, 1, 256, 256), "Output shape mismatch!"
print("✓ Shape check passed!")

# Test 2: Different input sizes
print("\nTest 2: Different input sizes")
for size in [128, 256, 512]:
    x = torch.randn(1, 3, size, size)
    output = model(x)
    print(f"✓ Input {size}x{size} -> Output {output.shape[2]}x{output.shape[3]}")
    assert output.shape[2:] == x.shape[2:], f"Spatial dimension mismatch for {size}x{size}!"

# Test 3: Gradient flow
print("\nTest 3: Gradient flow")
x = torch.randn(1, 3, 256, 256, requires_grad=True)
output = model(x)
loss = output.mean()
loss.backward()
print(f"✓ Input gradient shape: {x.grad.shape}")
print(f"✓ Input gradient norm: {x.grad.norm().item():.4f}")
assert x.grad is not None, "No gradient computed!"
print("✓ Gradient flow check passed!")

# Test 4: Model parameters
print("\nTest 4: Model parameters")
total_params = sum(p.numel() for p in model.parameters())
trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
print(f"✓ Total parameters: {total_params:,}")
print(f"✓ Trainable parameters: {trainable_params:,}")

# Test 5: Memory efficiency
print("\nTest 5: Memory test with batch")
try:
    x = torch.randn(4, 3, 512, 512)
    output = model(x)
    print(f"✓ Successfully processed batch shape: {x.shape}")
    print(f"✓ Output shape: {output.shape}")
except RuntimeError as e:
    if "out of memory" in str(e):
        print("⚠ Out of memory for 4x512x512 batch (expected on smaller GPUs)")
    else:
        raise

print("\n=== ALL TESTS PASSED! ===")
print("\nU-Mamba architecture summary:")
print("- 5 encoder blocks with skip connections")
print("- 5 decoder blocks for symmetric architecture") 
print("- Mamba blocks in deeper layers for global context")
print("- Proper channel alignment throughout the network")