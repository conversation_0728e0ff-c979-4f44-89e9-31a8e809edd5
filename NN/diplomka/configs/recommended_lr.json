{"learning_rates": {"comment": "Empirically determined learning rates for medical image segmentation", "resunet": {"adamw": 0.001, "adam": 0.001, "sgd": 0.01, "notes": "ResUNet works well with standard U-Net learning rates"}, "hrnet": {"adamw": 0.0005, "adam": 0.0005, "sgd": 0.005, "notes": "HRNet requires slightly lower LR due to complex multi-resolution fusion"}, "pspnet": {"adamw": 0.001, "adam": 0.001, "sgd": 0.01, "notes": "PSPNet with pretrained backbone can use standard rates"}, "transunet": {"adamw": 0.001, "adam": 0.001, "sgd": 0.005, "notes": "TransUNet transformer components benefit from AdamW"}}, "batch_size_adjustments": {"comment": "Linear scaling rule: lr = base_lr * (batch_size / base_batch_size)", "base_batch_size": 8, "scaling_factor": "sqrt", "notes": "For batch sizes > 32, use sqrt scaling instead of linear"}, "scheduler_recommendations": {"resunet": "cosine", "hrnet": "onecycle", "pspnet": "reduce", "transunet": "cosine"}, "warmup": {"enabled": true, "epochs": 5, "initial_lr_factor": 0.1, "notes": "Warmup helps with large models and prevents early divergence"}}