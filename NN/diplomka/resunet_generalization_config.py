"""
Configuration file for ResUNet models with enhanced generalization.
Contains optimized hyperparameters for better generalization and reduced overfitting.
"""

import torch.optim as optim
from torch.optim.lr_scheduler import CosineAnnealingLR, ReduceLROnPlateau

# Model configurations with reduced capacity for better generalization
MODEL_CONFIGS = {
    "BasicResUNet": {
        "features": [24, 48, 96, 192, 256],  # Aggressive reduction for better generalization
        "dropout_rate": 0.15,
        "use_instance_norm": True,
        "use_deep_supervision": False
    },

    "OptimizedResUNet": {
        "features": [16, 32, 64, 128],  # Aggressive capacity reduction for better generalization
        "dropout_rate": 0.2,
        "use_instance_norm": True,
        "use_checkpoint": False
    },

    "EnhancedResUNet": {
        "features": [16, 32, 64, 128],  # Aggressive capacity reduction for generalization
        "dropout_rate": 0.2,
        "attention_type": "cbam",
        "use_instance_norm": True
    },

    "AdvancedResUNet": {
        "features": [20, 40, 80, 160],  # Further reduced capacity for better generalization
        "dropout_rate": 0.2,
        "use_simam": True,
        "use_instance_norm": True
    }
}

# Training configurations optimized for generalization
TRAINING_CONFIGS = {
    "optimizer": {
        "type": "AdamW",  # AdamW with weight decay for better generalization
        "lr": 1e-4,       # Conservative learning rate
        "weight_decay": 1e-4,  # L2 regularization
        "betas": (0.9, 0.999),
        "eps": 1e-8
    },
    
    "scheduler": {
        "type": "CosineAnnealingLR",  # Smooth learning rate decay
        "T_max": 100,  # Number of epochs for full cycle
        "eta_min": 1e-6  # Minimum learning rate
    },
    
    "alternative_scheduler": {
        "type": "ReduceLROnPlateau",
        "mode": "min",
        "factor": 0.5,
        "patience": 10,
        "min_lr": 1e-6
    },
    
    "regularization": {
        "label_smoothing": 0.1,  # Label smoothing for better generalization
        "mixup_alpha": 0.2,      # Mixup augmentation
        "cutmix_alpha": 1.0,     # CutMix augmentation
        "gradient_clipping": 1.0  # Gradient clipping for stability
    },
    
    "training": {
        "batch_size": 8,         # Smaller batch size for better generalization
        "accumulation_steps": 4,  # Gradient accumulation to simulate larger batches
        "epochs": 200,
        "early_stopping_patience": 20,
        "validation_frequency": 5,
        "save_best_only": True
    },
    
    "data_augmentation": {
        "horizontal_flip": 0.5,
        "vertical_flip": 0.5,
        "rotation": 15,          # Degrees
        "scale": (0.8, 1.2),
        "brightness": 0.2,
        "contrast": 0.2,
        "gaussian_noise": 0.01,
        "elastic_transform": True
    }
}

# Loss function configurations
LOSS_CONFIGS = {
    "primary_loss": {
        "type": "DiceBCELoss",
        "dice_weight": 0.7,
        "bce_weight": 0.3,
        "smooth": 1e-6
    },
    
    "deep_supervision": {
        "enabled": False,
        "weights": [0.5, 0.3, 0.2],  # Weights for different supervision levels
        "loss_type": "DiceBCELoss"
    },
    
    "focal_loss": {
        "enabled": False,
        "alpha": 0.25,
        "gamma": 2.0
    }
}

# Validation and testing configurations
VALIDATION_CONFIGS = {
    "metrics": [
        "dice_coefficient",
        "iou",
        "precision",
        "recall",
        "hausdorff_distance",
        "surface_distance"
    ],
    
    "test_time_augmentation": {
        "enabled": True,
        "n_augmentations": 4,
        "merge_mode": "mean"
    },
    
    "post_processing": {
        "remove_small_objects": True,
        "min_object_size": 100,
        "morphological_closing": True,
        "kernel_size": 3
    }
}

def get_optimizer(model, config_name="optimizer"):
    """
    Get optimizer with weight decay for better generalization.
    
    Args:
        model: PyTorch model
        config_name: Configuration key in TRAINING_CONFIGS
    
    Returns:
        Configured optimizer
    """
    config = TRAINING_CONFIGS[config_name]
    
    if config["type"] == "AdamW":
        return optim.AdamW(
            model.parameters(),
            lr=config["lr"],
            weight_decay=config["weight_decay"],
            betas=config["betas"],
            eps=config["eps"]
        )
    elif config["type"] == "Adam":
        return optim.Adam(
            model.parameters(),
            lr=config["lr"],
            weight_decay=config["weight_decay"],
            betas=config["betas"],
            eps=config["eps"]
        )
    else:
        raise ValueError(f"Unsupported optimizer type: {config['type']}")

def get_scheduler(optimizer, config_name="scheduler"):
    """
    Get learning rate scheduler for better training dynamics.
    
    Args:
        optimizer: PyTorch optimizer
        config_name: Configuration key in TRAINING_CONFIGS
    
    Returns:
        Configured scheduler
    """
    config = TRAINING_CONFIGS[config_name]
    
    if config["type"] == "CosineAnnealingLR":
        return CosineAnnealingLR(
            optimizer,
            T_max=config["T_max"],
            eta_min=config["eta_min"]
        )
    elif config["type"] == "ReduceLROnPlateau":
        return ReduceLROnPlateau(
            optimizer,
            mode=config["mode"],
            factor=config["factor"],
            patience=config["patience"],
            min_lr=config["min_lr"]
        )
    else:
        raise ValueError(f"Unsupported scheduler type: {config['type']}")

def get_model_config(model_name):
    """
    Get model configuration for a specific ResUNet variant.
    
    Args:
        model_name: Name of the model variant
    
    Returns:
        Model configuration dictionary
    """
    if model_name not in MODEL_CONFIGS:
        raise ValueError(f"Unknown model: {model_name}. Available: {list(MODEL_CONFIGS.keys())}")
    
    return MODEL_CONFIGS[model_name].copy()

def print_config_summary():
    """Print a summary of all configurations."""
    print("=" * 80)
    print("ResUNet Generalization Configuration Summary")
    print("=" * 80)
    
    print("\nModel Configurations:")
    for model_name, config in MODEL_CONFIGS.items():
        print(f"  {model_name}:")
        print(f"    Features: {config['features']}")
        print(f"    Dropout: {config['dropout_rate']}")
    
    print(f"\nTraining Configuration:")
    print(f"  Optimizer: {TRAINING_CONFIGS['optimizer']['type']}")
    print(f"  Learning Rate: {TRAINING_CONFIGS['optimizer']['lr']}")
    print(f"  Weight Decay: {TRAINING_CONFIGS['optimizer']['weight_decay']}")
    print(f"  Batch Size: {TRAINING_CONFIGS['training']['batch_size']}")
    print(f"  Epochs: {TRAINING_CONFIGS['training']['epochs']}")
    
    print(f"\nRegularization Features:")
    print(f"  Label Smoothing: {TRAINING_CONFIGS['regularization']['label_smoothing']}")
    print(f"  Mixup Alpha: {TRAINING_CONFIGS['regularization']['mixup_alpha']}")
    print(f"  Gradient Clipping: {TRAINING_CONFIGS['regularization']['gradient_clipping']}")

if __name__ == "__main__":
    print_config_summary()
