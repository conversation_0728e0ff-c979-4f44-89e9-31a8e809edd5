#!/usr/bin/env python3
"""
Comprehensive validation test for enhanced ResUNet models with improved generalization.
Tests model loading, parameter count, input/output compatibility, and forward pass functionality.
"""

import torch
import torch.nn as nn
import sys
import os
from pathlib import Path

# Add the models directory to the path
sys.path.append(str(Path(__file__).parent / "models"))

# Import all ResUNet variants
from resunet import ResUNet as BasicResUNet
from resunet_optimized import OptimizedResUNet
from resunet_enhanced import EnhancedResUNet
from resunet_advanced import AdvancedResUNet

def count_parameters(model):
    """Count the total number of trainable parameters in a model."""
    return sum(p.numel() for p in model.parameters() if p.requires_grad)

def test_model_loading(model_class, model_name, **kwargs):
    """Test if model can be instantiated successfully."""
    try:
        model = model_class(**kwargs)
        print(f"✓ {model_name}: Model loaded successfully")
        return model
    except Exception as e:
        print(f"✗ {model_name}: Failed to load model - {e}")
        return None

def test_parameter_count(model, model_name, expected_reduction=0.5):
    """Test if parameter count is approximately halved."""
    if model is None:
        return False
    
    param_count = count_parameters(model)
    print(f"  {model_name}: {param_count:,} parameters")
    
    # For reference, original models had approximately:
    # BasicResUNet: ~34M parameters
    # OptimizedResUNet: ~8M parameters  
    # EnhancedResUNet: ~8M parameters
    # AdvancedResUNet: ~16M parameters
    
    reference_counts = {
        "BasicResUNet": 34_000_000,
        "OptimizedResUNet": 8_000_000,
        "EnhancedResUNet": 8_000_000,
        "AdvancedResUNet": 16_000_000
    }
    
    if model_name in reference_counts:
        expected_count = reference_counts[model_name] * expected_reduction
        reduction_ratio = param_count / reference_counts[model_name]
        print(f"  {model_name}: Parameter reduction ratio: {reduction_ratio:.2f}")
        
        if reduction_ratio <= 0.6:  # Allow some tolerance
            print(f"✓ {model_name}: Parameter count successfully reduced")
            return True
        else:
            print(f"⚠ {model_name}: Parameter count may not be sufficiently reduced")
            return False
    
    return True

def test_input_output_compatibility(model, model_name, input_size=(1, 3, 1024, 1024)):
    """Test 1024x1024 input/output compatibility."""
    if model is None:
        return False
    
    try:
        model.eval()
        with torch.no_grad():
            # Test with 1024x1024 input
            x = torch.randn(input_size)
            output = model(x)
            
            # Check output shape
            expected_output_shape = (input_size[0], 1, input_size[2], input_size[3])
            
            if isinstance(output, tuple):  # Handle deep supervision
                output = output[0]
            
            if output.shape == expected_output_shape:
                print(f"✓ {model_name}: Input/Output shapes compatible - {input_size} -> {output.shape}")
                return True
            else:
                print(f"✗ {model_name}: Shape mismatch - Expected {expected_output_shape}, got {output.shape}")
                return False
                
    except Exception as e:
        print(f"✗ {model_name}: Forward pass failed - {e}")
        return False

def test_forward_pass_stability(model, model_name, num_tests=3):
    """Test forward pass stability with multiple random inputs."""
    if model is None:
        return False
    
    try:
        model.eval()
        with torch.no_grad():
            for i in range(num_tests):
                x = torch.randn(1, 3, 512, 512)  # Smaller size for speed
                output = model(x)
                
                if isinstance(output, tuple):
                    output = output[0]
                
                # Check for NaN or Inf values
                if torch.isnan(output).any() or torch.isinf(output).any():
                    print(f"✗ {model_name}: Forward pass produced NaN/Inf values")
                    return False
            
            print(f"✓ {model_name}: Forward pass stable across {num_tests} tests")
            return True
            
    except Exception as e:
        print(f"✗ {model_name}: Forward pass stability test failed - {e}")
        return False

def test_regularization_features(model, model_name):
    """Test if regularization features are properly implemented."""
    if model is None:
        return False
    
    dropout_count = 0
    norm_count = 0
    
    for module in model.modules():
        if isinstance(module, (nn.Dropout, nn.Dropout2d)):
            dropout_count += 1
        elif isinstance(module, (nn.BatchNorm2d, nn.InstanceNorm2d)):
            norm_count += 1
    
    print(f"  {model_name}: {dropout_count} dropout layers, {norm_count} normalization layers")
    
    if dropout_count > 0 and norm_count > 0:
        print(f"✓ {model_name}: Regularization features properly implemented")
        return True
    else:
        print(f"⚠ {model_name}: Limited regularization features detected")
        return False

def main():
    """Run comprehensive validation tests."""
    print("=" * 80)
    print("ResUNet Generalization Enhancement Validation Tests")
    print("=" * 80)
    
    # Test configurations for each model with updated capacity reduction
    test_configs = [
        (BasicResUNet, "BasicResUNet", {"features": [24, 48, 96, 192, 256], "dropout_rate": 0.15}),
        (OptimizedResUNet, "OptimizedResUNet", {"features": [16, 32, 64, 128], "dropout_rate": 0.2}),
        (EnhancedResUNet, "EnhancedResUNet", {"features": [16, 32, 64, 128], "dropout_rate": 0.2}),
        (AdvancedResUNet, "AdvancedResUNet", {"features": [20, 40, 80, 160], "dropout_rate": 0.2})
    ]
    
    results = {}
    
    for model_class, model_name, kwargs in test_configs:
        print(f"\n{'-' * 60}")
        print(f"Testing {model_name}")
        print(f"{'-' * 60}")
        
        # Test 1: Model Loading
        model = test_model_loading(model_class, model_name, **kwargs)
        
        # Test 2: Parameter Count
        param_test = test_parameter_count(model, model_name)
        
        # Test 3: Input/Output Compatibility
        io_test = test_input_output_compatibility(model, model_name)
        
        # Test 4: Forward Pass Stability
        stability_test = test_forward_pass_stability(model, model_name)
        
        # Test 5: Regularization Features
        reg_test = test_regularization_features(model, model_name)
        
        # Store results
        results[model_name] = {
            "loaded": model is not None,
            "parameters": param_test,
            "io_compatibility": io_test,
            "stability": stability_test,
            "regularization": reg_test
        }
    
    # Summary
    print(f"\n{'=' * 80}")
    print("VALIDATION SUMMARY")
    print(f"{'=' * 80}")
    
    for model_name, tests in results.items():
        passed = sum(tests.values())
        total = len(tests)
        status = "PASS" if passed == total else "PARTIAL" if passed > 0 else "FAIL"
        print(f"{model_name:20} | {passed}/{total} tests passed | {status}")
    
    print(f"\n{'=' * 80}")
    print("Validation completed!")
    print(f"{'=' * 80}")

if __name__ == "__main__":
    main()
