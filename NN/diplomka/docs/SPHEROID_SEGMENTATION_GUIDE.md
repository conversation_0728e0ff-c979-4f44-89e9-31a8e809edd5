# Spheroid Segmentation Training Guide

## Overview
Tato příručka obsahuje implementaci a doporučení pro trénování segmentačních modelů na spheroid datasetu s využitím 2x NVIDIA L40S GPU.

## Implementované modely

### 1. ResUNet (původní)
- Enhanced ResUNet s attention gates a SE bloky
- Features: [64, 128, 256, 512, 1024]
- Instance Normalization pro lepší generalizaci na mikroskopických datech
- Vhodný pro detailní segmentaci s vysokým rozlišením

### 2. HRNet (High-Resolution Network)
- Udržuje vysoké rozlišení po celou dobu zpracování
- 4 paralelní větve s různými rozlišeními
- Instance Normalization implementována
- Excelentní pro zachování hraničních detailů
- **DOPORUČENO** pro spheroid segmentaci kvůli zachování jemných struktur

### 3. PSPNet (Pyramid Scene Parsing Network)
- Pyramid Pooling Module pro multi-scale context
- ResNet101 backbone pro podobnou kapacitu jako ResUNet
- Instance Normalization v pyramid pooling a finálních vrstvách
- Auxiliary loss branch pro lepší trénování
- Vhodný pro objekty různých velikostí

### 4. TransUNet (Transformer + U-Net)
- Hybridní CNN-Transformer architektura
- Vision Transformer (ViT) pro globální kontext
- CNN encoder/decoder pro lokální rysy
- Instance Normalization v CNN částech
- Vhodný pro komplexní vzory vyžadující globální pochopení
- Podporuje vstup 1024×1024 nativně

## Hlavní vylepšení

### 1. Multi-GPU podpora
- Distributed Data Parallel (DDP) pro 2x NVIDIA L40S
- Automatické rozdělení batch size mezi GPU
- Synchronizované batch normalization

### 2. Pokročilé augmentace
```python
- RandomRotate90 - rotace o 90° (důležité pro mikroskopické snímky)
- ElasticTransform - elastické deformace
- OpticalDistortion - optické zkreslení
- CoarseDropout - simulace částečně zakrytých oblastí
- CLAHE - adaptivní vyrovnání histogramu
```

### 3. Kombinovaná loss funkce
- **Focal Loss** - řeší class imbalance (pozadí vs. spheroidy)
- **Dice Loss** - optimalizuje překryv oblastí
- **IoU Loss** - přímá optimalizace IoU metriky
- Nastavitelné váhy pro každou komponentu

### 4. Learning Rate Schedulery
- **OneCycleLR** - super-convergence, doporučeno pro HRNet
- **CosineAnnealingWarmRestarts** - periodické restarty, dobré pro dlouhé trénování
- **ReduceLROnPlateau** - adaptivní snižování, konzervativní přístup

### 5. Optimizéry
- **AdamW** - Adam s weight decay decoupling, doporučeno
- **SGD** - klasický, dobré výsledky s PSPNet
- **Adam** - rychlá konvergence

### 6. Test Time Augmentation (TTA)
- Implementováno pro validation a testing
- 8 různých augmentací (rotace, flipy, transpozice)
- Průměrování predikcí pro lepší výsledky
- Aktivovat pomocí `--use_tta`

### 7. Instance Normalization
- Nahrazuje Batch Normalization
- Lepší pro mikroskopické snímky s různými podmínkami osvětlení
- Méně závislé na velikosti batch
- Aktivováno defaultně pomocí `--use_instance_norm`

## Doporučené nastavení

### Pro HRNet (nejlepší pro spheroidy):
```bash
python CNN_main_spheroid.py \
    --model hrnet \
    --batch_size 12 \
    --lr 5e-4 \
    --optimizer adamw \
    --scheduler onecycle \
    --img_size 512 \
    --gpus 2
```

### Pro ResUNet (baseline):
```bash
python CNN_main_spheroid.py \
    --model resunet \
    --batch_size 16 \
    --lr 1e-3 \
    --optimizer adamw \
    --scheduler cosine \
    --img_size 512 \
    --gpus 2
```

### Pro PSPNet (pro různé velikosti):
```bash
python CNN_main_spheroid.py \
    --model pspnet \
    --batch_size 10 \
    --lr 1e-3 \
    --optimizer sgd \
    --scheduler reduce \
    --img_size 512 \
    --gpus 2
```

## Další doporučená vylepšení

### 1. Test Time Augmentation (TTA)
```python
# Přidat do evaluate_model.py
def apply_tta(model, image, device):
    """Test time augmentation"""
    # Original
    pred1 = torch.sigmoid(model(image))
    
    # Horizontal flip
    pred2 = torch.sigmoid(model(torch.flip(image, dims=[3])))
    pred2 = torch.flip(pred2, dims=[3])
    
    # Vertical flip
    pred3 = torch.sigmoid(model(torch.flip(image, dims=[2])))
    pred3 = torch.flip(pred3, dims=[2])
    
    # 90° rotations
    pred4 = torch.sigmoid(model(torch.rot90(image, k=1, dims=[2,3])))
    pred4 = torch.rot90(pred4, k=-1, dims=[2,3])
    
    # Average predictions
    return (pred1 + pred2 + pred3 + pred4) / 4
```

### 2. Semi-supervised learning
- Využít unlabeled data z dalších datasetů
- Pseudo-labeling nebo consistency regularization
- Mean Teacher nebo FixMatch přístupy

### 3. Instance normalization pro microscopy
```python
# Nahradit BatchNorm za InstanceNorm v modelech
nn.InstanceNorm2d(num_features, affine=True)
```

### 4. Ensemble metody
```python
# Kombinace modelů
predictions = []
for model_name in ['resunet', 'hrnet', 'pspnet']:
    model = load_model(model_name)
    pred = model(image)
    predictions.append(pred)

# Weighted average
final_pred = 0.4 * predictions[0] + 0.4 * predictions[1] + 0.2 * predictions[2]
```

### 5. Post-processing
```python
# Morfologické operace
from scipy.ndimage import binary_fill_holes, binary_opening

def post_process(mask):
    # Remove small objects
    mask = binary_opening(mask, structure=np.ones((3,3)))
    # Fill holes
    mask = binary_fill_holes(mask)
    return mask
```

### 6. Active Learning
- Identifikovat nejisté případy
- Prioritizovat anotaci těžkých příkladů
- Iterativní vylepšování datasetu

## Monitorování a debugging

### TensorBoard
```bash
tensorboard --logdir=./outputs/*/tensorboard
```

### Metriky k sledování:
- IoU (hlavní metrika)
- Dice coefficient
- Boundary IoU (pro hodnocení hran)
- Per-class metrics (pokud máte více typů spheroidů)

## Hardware optimalizace pro L40S

### 1. Mixed Precision Training
- Již implementováno pomocí torch.cuda.amp
- L40S má Tensor Cores - významné zrychlení

### 2. Gradient Accumulation (pro větší efektivní batch size)
```python
accumulation_steps = 4
for i, (images, masks) in enumerate(train_loader):
    loss = criterion(model(images), masks)
    loss = loss / accumulation_steps
    loss.backward()
    
    if (i + 1) % accumulation_steps == 0:
        optimizer.step()
        optimizer.zero_grad()
```

### 3. Pin Memory a Persistent Workers
- Již nastaveno v data loaderech
- num_workers=8 optimální pro 2 GPU

## Očekávané výsledky

Na základě podobných medical imaging úloh:
- **IoU**: 0.85-0.92
- **Dice**: 0.92-0.96
- **Training time**: 2-4 hodiny na 100 epoch (2x L40S)

## Troubleshooting

### Out of Memory
- Snížit batch_size
- Snížit img_size na 384 nebo 256
- Použít gradient accumulation

### Pomalé trénování
- Zkontrolovat GPU utilization: `nvidia-smi`
- Zvýšit num_workers
- Použít persistent_workers=True

### Přetrénování
- Zvýšit weight_decay
- Přidat více augmentací
- Snížit learning rate
- Použít early stopping (již implementováno)

## Závěr

Implementoval jsem kompletní pipeline pro trénování state-of-the-art segmentačních modelů na tvém spheroid datasetu. HRNet je doporučený jako primární model díky schopnosti udržet vysoké rozlišení, což je klíčové pro mikroskopické snímky. Všechny modely jsou optimalizovány pro multi-GPU trénování na 2x NVIDIA L40S.