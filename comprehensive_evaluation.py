#!/usr/bin/env python3
"""
Comprehensive evaluation script for all models on multiple test datasets.
Handles architecture compatibility issues and evaluates on both SpheroSeg and DTS datasets.
"""

import os
import sys
import time
import argparse
import json
from pathlib import Path
import numpy as np
import torch
import torch.nn.functional as F
from torch.utils.data import DataLoader
from PIL import Image
import cv2
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import matplotlib
matplotlib.use('Agg')  # Non-interactive backend
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
from datetime import datetime

# Import model architectures
sys.path.append('/home/<USER>/SpheroSeg/NN/diplomka')
from models.resunet import ResUNet
from models.resunet_small import ResUNetSmall
from models.hrnet import HRNetV2
from models.pspnet_stable import PSPNet as StabilizedPSPNet
from models.pspnet import PSPNet as OriginalPSPNet
from models.resunet_advanced import AdvancedResUNet

class SegmentationDataset(torch.utils.data.Dataset):
    """Dataset for loading images and masks"""
    
    def __init__(self, images_dir, masks_dir, transform=None):
        self.images_dir = Path(images_dir)
        self.masks_dir = Path(masks_dir)
        self.transform = transform
        
        # Get all image files
        self.image_files = sorted([f for f in self.images_dir.glob('*') 
                                 if f.suffix.lower() in ['.jpg', '.jpeg', '.png', '.tif', '.tiff']])
        
        # Filter to only include images that have corresponding masks
        self.valid_pairs = []
        for img_path in self.image_files:
            mask_path = self.masks_dir / f"{img_path.stem}.png"
            if mask_path.exists():
                self.valid_pairs.append((img_path, mask_path))
        
        print(f"Found {len(self.image_files)} images, {len(self.valid_pairs)} valid image-mask pairs")
    
    def __len__(self):
        return len(self.valid_pairs)
    
    def __getitem__(self, idx):
        img_path, mask_path = self.valid_pairs[idx]
        
        # Load image
        image = cv2.imread(str(img_path))
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Load mask
        mask = cv2.imread(str(mask_path), cv2.IMREAD_GRAYSCALE)
        
        # Resize to 1024x1024
        image = cv2.resize(image, (1024, 1024))
        mask = cv2.resize(mask, (1024, 1024))
        
        # Normalize
        image = image.astype(np.float32) / 255.0
        mask = (mask > 127).astype(np.float32)
        
        # Convert to tensors
        image = torch.from_numpy(image).permute(2, 0, 1)
        mask = torch.from_numpy(mask).unsqueeze(0)
        
        return image, mask, str(img_path.name)

def safe_load_checkpoint(model_path, device):
    """Safely load checkpoint with PyTorch 2.6+ compatibility"""
    try:
        # Try with weights_only=True first (safer) with safe globals for argparse.Namespace
        import argparse
        with torch.serialization.safe_globals([argparse.Namespace]):
            checkpoint = torch.load(model_path, map_location=device, weights_only=True)
    except Exception as e:
        print(f"  Warning: weights_only=True with safe globals failed ({e}), trying weights_only=False...")
        # Fallback to weights_only=False (trusted source)
        checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    return checkpoint

def detect_pspnet_architecture(checkpoint_path):
    """Detect which PSPNet architecture was used based on checkpoint structure"""
    try:
        checkpoint = safe_load_checkpoint(checkpoint_path, 'cpu')
        state_dict = checkpoint['model_state_dict'] if 'model_state_dict' in checkpoint else checkpoint
        
        # Check PPM stage channel dimensions to determine architecture
        for key in state_dict.keys():
            if 'ppm.stages.0.1.weight' in key:
                channels = state_dict[key].shape[0]
                if channels == 512:
                    return 'original'  # Original PSPNet
                elif channels == 256:
                    return 'stabilized'  # Stabilized PSPNet
        
        # Check final layer structure
        final_keys = [k for k in state_dict.keys() if 'final' in k]
        if any('final.4.weight' in k for k in final_keys):
            return 'original'
        elif any('final.8.weight' in k for k in final_keys):
            return 'stabilized'
            
        return 'unknown'
    except Exception as e:
        print(f"Error detecting PSPNet architecture: {e}")
        return 'unknown'

def load_model(model_name, model_path, device):
    """Load model with architecture compatibility handling"""
    print(f"  Loading model {model_name}...")
    
    if model_name == 'resunet':
        model = ResUNet(in_channels=3, out_channels=1, use_instance_norm=True)
    elif model_name == 'resunet_small':
        model = ResUNetSmall(in_channels=3, out_channels=1, use_instance_norm=True)
    elif model_name == 'hrnet':
        model = HRNetV2(n_class=1, use_instance_norm=True)
    elif model_name == 'pspnet':
        # Detect which PSPNet architecture to use
        arch_type = detect_pspnet_architecture(model_path)
        print(f"  Detected PSPNet architecture: {arch_type}")
        
        if arch_type == 'original':
            model = OriginalPSPNet(n_class=1, backbone='resnet101', use_instance_norm=True)
        elif arch_type == 'stabilized':
            model = StabilizedPSPNet(n_class=1, backbone='resnet50', use_instance_norm=True)
        else:
            # Default to stabilized for safety
            print(f"  Unknown architecture, defaulting to stabilized PSPNet")
            model = StabilizedPSPNet(n_class=1, backbone='resnet50', use_instance_norm=True)
    elif model_name == 'resunet_advanced':
        model = AdvancedResUNet(in_channels=3, out_channels=1, use_instance_norm=True)
    else:
        raise ValueError(f"Unknown model: {model_name}")

    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"  Total parameters: {total_params:,}")
    print(f"  Trainable parameters: {trainable_params:,}")

    # Load weights
    checkpoint = safe_load_checkpoint(model_path, device)
    
    if 'model_state_dict' in checkpoint:
        try:
            model.load_state_dict(checkpoint['model_state_dict'], strict=True)
            print(f"  Model loaded successfully (strict=True)")
        except RuntimeError as e:
            print(f"  Warning: Strict loading failed, trying with strict=False")
            print(f"  Error: {str(e)[:200]}...")
            missing_keys, unexpected_keys = model.load_state_dict(checkpoint['model_state_dict'], strict=False)
            if missing_keys:
                print(f"  Missing keys: {len(missing_keys)}")
            if unexpected_keys:
                print(f"  Unexpected keys: {len(unexpected_keys)}")
        
        if 'epoch' in checkpoint:
            print(f"  Model trained for {checkpoint['epoch']} epochs")
        if 'best_iou' in checkpoint:
            print(f"  Best IoU from training: {checkpoint.get('best_iou', 'N/A'):.4f}")
    else:
        model.load_state_dict(checkpoint)

    model.to(device)
    model.eval()
    return model

def calculate_metrics(y_true, y_pred, threshold=0.5):
    """Calculate comprehensive metrics"""
    y_pred_binary = (y_pred > threshold).astype(np.uint8)
    y_true_binary = y_true.astype(np.uint8)
    
    # Flatten arrays
    y_true_flat = y_true_binary.flatten()
    y_pred_flat = y_pred_binary.flatten()
    
    # Basic metrics
    accuracy = accuracy_score(y_true_flat, y_pred_flat)
    precision = precision_score(y_true_flat, y_pred_flat, zero_division=0)
    recall = recall_score(y_true_flat, y_pred_flat, zero_division=0)
    f1 = f1_score(y_true_flat, y_pred_flat, zero_division=0)
    
    # IoU and Dice
    intersection = np.sum(y_true_binary * y_pred_binary)
    union = np.sum(y_true_binary) + np.sum(y_pred_binary) - intersection
    iou = intersection / (union + 1e-8)
    dice = 2 * intersection / (np.sum(y_true_binary) + np.sum(y_pred_binary) + 1e-8)
    
    # Specificity
    tn = np.sum((1 - y_true_flat) * (1 - y_pred_flat))
    fp = np.sum((1 - y_true_flat) * y_pred_flat)
    specificity = tn / (tn + fp + 1e-8)
    
    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1,
        'iou': iou,
        'dice': dice,
        'specificity': specificity
    }

def evaluate_model(model, dataloader, device, model_name, threshold=0.55):
    """Evaluate model on dataset"""
    print(f"Evaluating model {model_name}...")
    
    all_predictions = []
    all_targets = []
    inference_times = []
    
    # Warm-up
    print("  Performing warm-up...")
    with torch.no_grad():
        for i, (images, masks, _) in enumerate(dataloader):
            if i >= 3:  # 3 warm-up iterations
                break
            images = images.to(device)
            _ = model(images)
    
    print("  Running evaluation...")
    with torch.no_grad():
        for batch_idx, (images, masks, filenames) in enumerate(dataloader):
            images = images.to(device)
            masks = masks.numpy()
            
            # Measure inference time
            torch.cuda.synchronize() if device.type == 'cuda' else None
            start_time = time.time()
            
            outputs = model(images)
            outputs = torch.sigmoid(outputs)
            
            torch.cuda.synchronize() if device.type == 'cuda' else None
            end_time = time.time()
            
            inference_times.append(end_time - start_time)
            
            # Convert to numpy
            predictions = outputs.cpu().numpy()
            
            all_predictions.extend(predictions)
            all_targets.extend(masks)
            
            if (batch_idx + 1) % 50 == 0:
                print(f"  Processed {batch_idx + 1}/{len(dataloader)} batches")
    
    # Calculate metrics
    all_predictions = np.array(all_predictions)
    all_targets = np.array(all_targets)
    
    metrics = calculate_metrics(all_targets, all_predictions, threshold)
    
    # Inference time statistics
    avg_inference_time = np.mean(inference_times)
    std_inference_time = np.std(inference_times)
    
    metrics.update({
        'avg_inference_time': avg_inference_time,
        'std_inference_time': std_inference_time,
        'optimal_threshold': threshold
    })
    
    print(f"  Results: IoU={metrics['iou']:.4f}, Dice={metrics['dice']:.4f}, F1={metrics['f1_score']:.4f}")
    
    return metrics, inference_times

def main():
    """Main evaluation function"""
    print("=== Comprehensive Model Evaluation ===\n")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Define datasets
    datasets = {
        'SpheroSeg': {
            'images_dir': '/data/prusek/training_small/test/images',
            'masks_dir': '/data/prusek/training_small/test/masks'
        },
        'DTS': {
            'images_dir': '/data/prusek/DTS/images',
            'masks_dir': '/data/prusek/DTS/masks'
        }
    }
    
    # Define models and their checkpoint paths
    model_configs = {
        'resunet': './scripts/training/outputs/resunet_finetuned/best_model.pth',
        'resunet_small': './scripts/training/outputs/resunet_small_finetune_20250724_093452/best_model.pth',
        'hrnet': './scripts/training/outputs/hrnet_finetuned/best_model.pth',
        'pspnet': './scripts/training/outputs/pspnet_finetuned/best_model.pth',
        'resunet_advanced': './scripts/training/outputs/resunet_advanced_finetuned/best_model.pth'
    }
    
    # Evaluate on each dataset
    for dataset_name, dataset_paths in datasets.items():
        print(f"\n{'='*60}")
        print(f"Evaluating on {dataset_name} dataset")
        print(f"{'='*60}")
        
        # Check if dataset exists
        if not os.path.exists(dataset_paths['images_dir']) or not os.path.exists(dataset_paths['masks_dir']):
            print(f"Dataset {dataset_name} not found, skipping...")
            continue
        
        # Create dataset and dataloader
        dataset = SegmentationDataset(dataset_paths['images_dir'], dataset_paths['masks_dir'])
        if len(dataset) == 0:
            print(f"No valid samples found in {dataset_name}, skipping...")
            continue
            
        dataloader = DataLoader(dataset, batch_size=4, shuffle=False, num_workers=2, pin_memory=True)
        
        results = {}
        
        # Evaluate each model
        for model_name, checkpoint_path in model_configs.items():
            print(f"\n{'-'*50}")
            print(f"Evaluating model: {model_name}")
            print(f"Checkpoint: {checkpoint_path}")
            
            if not os.path.exists(checkpoint_path):
                print(f"Checkpoint not found: {checkpoint_path}")
                continue
            
            try:
                # Load model
                model = load_model(model_name, checkpoint_path, device)
                
                # Evaluate
                metrics, inference_times = evaluate_model(model, dataloader, device, model_name)
                results[model_name] = metrics
                
                # Clean up GPU memory
                del model
                torch.cuda.empty_cache()
                
            except Exception as e:
                print(f"Error evaluating {model_name}: {e}")
                continue
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = Path(f"evaluation_results_{dataset_name}_{timestamp}")
        output_dir.mkdir(exist_ok=True)
        
        # Save detailed results
        with open(output_dir / 'detailed_results.json', 'w') as f:
            json.dump({
                'dataset': dataset_name,
                'dataset_info': {
                    'images_dir': str(dataset_paths['images_dir']),
                    'masks_dir': str(dataset_paths['masks_dir']),
                    'num_samples': len(dataset),
                    'device': str(device)
                },
                'results': results,
                'timestamp': timestamp
            }, f, indent=2)
        
        # Create comparison CSV
        if results:
            df_data = []
            for model_name, metrics in results.items():
                df_data.append({
                    'Model': model_name,
                    'Accuracy': metrics['accuracy'],
                    'Precision': metrics['precision'],
                    'Recall': metrics['recall'],
                    'F1-Score': metrics['f1_score'],
                    'IoU': metrics['iou'],
                    'Dice': metrics['dice'],
                    'Specificity': metrics['specificity'],
                    'Avg_Inference_Time': metrics['avg_inference_time'],
                    'Std_Inference_Time': metrics['std_inference_time'],
                    'Optimal_Threshold': metrics['optimal_threshold']
                })
            
            df = pd.DataFrame(df_data)
            df = df.sort_values('IoU', ascending=False)
            df.to_csv(output_dir / 'model_comparison.csv', index=False)
            
            print(f"\nResults for {dataset_name}:")
            print(df.to_string(index=False))
        
        print(f"\nResults saved to: {output_dir}")

if __name__ == "__main__":
    main()
